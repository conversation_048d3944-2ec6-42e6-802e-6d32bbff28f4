﻿namespace GMCadiomCore.Services.Core
{
    public interface IBaseService<TEntity, TEntityView> where TEntity : class where TEntityView : class
    {
        void EditModelBeforeSave(TEntity model);
        void ValidateEntity(TEntity model, bool isEdit);
        TEntity SetEntity(TEntity model, TEntity entity);

        #region Async
        Task<ServiceResult<IEnumerable<TEntity>>> GetAllAsync();
        Task<ServiceResult<PaginationList<TEntity>>> GetAllPaginationListAsync(int pageNumber, int pageSize = 25);
        Task<ServiceResult<PaginationList<TEntity>>> GetAllByNameAsync(string name, int pageNumber, int pageSize = 25);
        Task<ServiceResult<PaginationList<TEntityView>>> GetAllViewAsync(int pageNumber, int pageSize = 25);
        Task<ServiceResult<PaginationList<TEntityView>>> GetAllViewByAsync(TEntityView searchValues, int pageNumber, int pageSize = 25);
        Task<ServiceResult<IEnumerable<IdAndName>>> GetSelectListAsync(Expression<Func<TEntity, bool>>? searchValue = null);
        Task<ServiceResult<TEntity?>> GetByIdAsync(Ulid Id);
        Task<ServiceResult<bool>> IsExistAsync(Ulid id);
        Task<ServiceResult<TEntity?>> AddAsync(TEntity model);
        Task<ServiceResult<TEntity?>> UpdateAsync(TEntity model);
        Task<ServiceResult<bool>> RemoveAsync(Ulid Id);
        #endregion

        #region Sync
        ServiceResult<IEnumerable<TEntity>> GetAll();
        ServiceResult<PaginationList<TEntity>> GetAllPaginationList(int pageNumber, int pageSize = 25);
        ServiceResult<PaginationList<TEntity>> GetAllByName(string name, int pageNumber, int pageSize = 25);
        ServiceResult<PaginationList<TEntityView>> GetAllView(int pageNumber, int pageSize = 25);
        ServiceResult<PaginationList<TEntityView>> GetAllViewBy(TEntityView searchValues, int pageNumber, int pageSize = 25);
        ServiceResult<IEnumerable<IdAndName>> GetSelectList(Expression<Func<TEntity, bool>>? searchValue = null);
        ServiceResult<TEntity?> GetById(Ulid Id);
        ServiceResult<bool> IsExist(Ulid id);
        ServiceResult<TEntity?> Add(TEntity model);
        ServiceResult<TEntity?> Update(TEntity model);
        ServiceResult<bool> Remove(Ulid Id);
        #endregion
    }
}
