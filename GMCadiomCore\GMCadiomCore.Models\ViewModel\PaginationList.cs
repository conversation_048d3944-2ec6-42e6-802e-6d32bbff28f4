﻿namespace GMCadiomCore.Models.ViewModel
{
    public class PaginationList
    {
        public object DataSource { get; protected set; }

        public int TotalCount { get; private set; }
        public int PageNumber { get; private set; }
        public int PageSize { get; private set; }

        public int TotalPages => (int)Math.Ceiling(TotalCount / (double)PageSize);

        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;

        public int FirstItemIndex => (PageNumber - 1) * PageSize + 1;
        public int LastItemIndex => Math.Min(PageNumber * PageSize, TotalCount);

        public PaginationList(int totalCount, int pageNumber, int pageSize)
        {
            TotalCount = totalCount;
            PageNumber = pageNumber;
            PageSize = pageSize;
        }
    }

    public class PaginationList<T> : PaginationList
    {
        public List<T> Items => DataSource as List<T> ?? new List<T>();

        public PaginationList(IEnumerable<T> items, int totalCount, int pageNumber, int pageSize) :
            base(totalCount, pageNumber, pageSize)
        {
            DataSource = items.ToList();
        }

        public static PaginationList<T> Create(IEnumerable<T> source, int pageNumber, int pageSize = 25)
        {
            var count = source.Count(); //total number of items in the source data.      
            var items = source.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();
            return new PaginationList<T>(items, count, pageNumber, pageSize);
        }
    }
}
