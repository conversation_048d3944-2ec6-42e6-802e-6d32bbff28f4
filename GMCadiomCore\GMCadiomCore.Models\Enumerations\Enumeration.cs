﻿namespace GMCadiomCore.Models.Enumerations
{
    public abstract class Enumeration<TKey, TEnum> : IEquatable<Enumeration<TKey, TEnum>> where TEnum : Enumeration<TKey, TEnum> where TKey : notnull
    {
        private static readonly Dictionary<TKey, TEnum> _enumerationsDictionary = CreateEnumerations();

        protected Enumeration(TKey key, string value)
        {
            Value = key;
            Name = value;
        }

        public TKey Value { get; protected set; }
        public string Name { get; protected set; } = string.Empty;

        public static TEnum FromValue(TKey value)
        {
            if (value is null)
                throw new ArgumentNullException(nameof(value));

            bool result = _enumerationsDictionary.TryGetValue(value, out TEnum? enumeration);

            if (enumeration == null)
                throw new KeyNotFoundException($"The value of {value} not found");

            return enumeration;
        }

        public static TEnum FromName(string name)
        {
            if (string.IsNullOrEmpty(name))
                throw new ArgumentNullException(nameof(name));

            TEnum? enumeration = _enumerationsDictionary.Values.SingleOrDefault(enumeration => enumeration.Name == name);

            if (enumeration == null)
                throw new KeyNotFoundException($"The value of {name} not found");

            return enumeration;
        }

        public bool Equals(Enumeration<TKey, TEnum>? other)
        {
            if (other is null)
                return false;
            return
                GetType() == other.GetType() &&
                Value!.Equals(other.Value);
        }

        public override bool Equals(object? obj)
        {
            return obj is Enumeration<TKey, TEnum> other && Equals(other);
        }

        public override int GetHashCode()
        {
            return Value!.GetHashCode();
        }

        public override string ToString()
        {
            return Name;
        }

        private static Dictionary<TKey, TEnum> CreateEnumerations()
        {
            var enumerationType = typeof(TEnum);

            var fieldsForType = enumerationType
                .GetFields(
                BindingFlags.Public |
                BindingFlags.Static |
                BindingFlags.FlattenHierarchy)
                .Where(fieldInfo =>
                enumerationType.IsAssignableFrom(fieldInfo.FieldType))
                .Select(fieldInfo =>
                (TEnum)fieldInfo.GetValue(default)!);

            return fieldsForType.ToDictionary(x => x.Value);
        }
    }
}
