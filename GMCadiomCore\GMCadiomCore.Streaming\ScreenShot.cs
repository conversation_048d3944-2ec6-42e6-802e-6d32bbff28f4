﻿namespace GMCadiomCore.Streaming
{
    static class ScreenShot
    {
        /// <summary>
        /// Returns a list of Snapshots
        /// </summary>
        /// <param name="delayTime"></param>
        /// <returns></returns>
        public static IEnumerable<Image> Snapshots(int PrimaryScreenWidth, int PrimaryScreenHeight, int width, int height, bool showCursor)
        {
            Size size = new Size(PrimaryScreenWidth, PrimaryScreenHeight);

            Bitmap srcImage = new Bitmap(size.Width, size.Height);
            Graphics srcGraphics = Graphics.FromImage(srcImage);
            srcGraphics.CompositingMode = CompositingMode.SourceCopy;
            srcGraphics.CompositingQuality = CompositingQuality.HighQuality;
            srcGraphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
            srcGraphics.SmoothingMode = SmoothingMode.HighQuality;
            srcGraphics.PixelOffsetMode = PixelOffsetMode.HighQuality;

            bool scaled = width != size.Width || height != size.Height;

            Bitmap dstImage = srcImage;
            Graphics dstGraphics = srcGraphics;

            if (scaled)
            {
                dstImage = new Bitmap(width, height);
                dstGraphics = Graphics.FromImage(dstImage);
                dstGraphics.CompositingMode = CompositingMode.SourceCopy;
                dstGraphics.CompositingQuality = CompositingQuality.HighQuality;
                dstGraphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                dstGraphics.SmoothingMode = SmoothingMode.HighQuality;
                dstGraphics.PixelOffsetMode = PixelOffsetMode.HighQuality;
            }

            Rectangle src = new Rectangle(0, 0, size.Width, size.Height);
            Rectangle dst = new Rectangle(0, 0, width, height);
            Size curSize = new Size(32, 32);

            while (true)
            {
                srcGraphics.CopyFromScreen(0, 0, 0, 0, size);

                //if (showCursor)
                //    Cursors.Default.Draw(srcGraphics, new Rectangle(Cursor.Position, curSize));

                if (scaled)
                    dstGraphics.DrawImage(srcImage, dst, src, GraphicsUnit.Pixel);

                yield return dstImage;

            }
        }

        internal static IEnumerable<MemoryStream> Streams(this IEnumerable<Image> source)
        {
            MemoryStream ms = new MemoryStream();

            foreach (var img in source)
            {
                ms.SetLength(0);
                img.Save(ms, ImageFormat.Jpeg);
                yield return ms;
            }

            ms.Close();
            ms = null!;

            yield break;
        }
    }
}
