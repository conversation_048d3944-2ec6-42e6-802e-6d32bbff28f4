﻿namespace GMCadiomCore.Desktop.Shared.Helper
{
    public static class Excel
    {
        public static void ExportFromDGV(DataGridView dgv)
        {
            if (dgv.Rows.Count > 0)
            {
                SaveFileDialog saveFile = new SaveFileDialog();
                saveFile.Filter = "Excel file|*.xlsx";
                saveFile.Title = "save results as Excel spreadsheet";
                saveFile.FileName = dgv.Name + " -" + DateTime.Now.ToString("yyyyMMdd") + ".xlsx";

                if (saveFile.ShowDialog() == DialogResult.OK)
                {
                    using (XLWorkbook workbook = new XLWorkbook())
                    {
                        //Creating DataTable
                        DataTable dt = new DataTable();

                        //Creating DataTable
                        List<DataGridViewColumn> dgvColumns = dgv.Columns.Cast<DataGridViewColumn>().Where(x => x.Visible == true).ToList();

                        //Add The Columns
                        foreach (DataGridViewColumn column in dgvColumns)
                            dt.Columns.Add(column.HeaderText, Nullable.GetUnderlyingType(column.ValueType) ?? column.ValueType);

                        //Add The Rows
                        foreach (DataGridViewRow row in dgv.Rows)
                        {
                            dt.Rows.Add();
                            foreach (DataGridViewCell cell in row.Cells)
                                if (cell.Visible == true)
                                    dt.Rows[dt.Rows.Count - 1][cell.ColumnIndex] = cell.Value ?? DBNull.Value;
                        }

                        workbook.Worksheets.Add(dt, dgv.Name);
                        //Save The Excel File
                        workbook.SaveAs(saveFile.FileName);
                    }
                }
            }
        }
    }
}
