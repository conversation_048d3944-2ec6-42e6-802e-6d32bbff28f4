﻿namespace GMCadiomCore.Desktop.CustomControls.dgv
{
    partial class PaginationDataGridView
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tlpMain = new TableLayoutPanel();
            dgvMain = new dgvTotalsSummary();
            btnFirst = new Button();
            btnPrevious = new Button();
            lblHeader = new Label();
            btnLast = new Button();
            btnNext = new Button();
            lblPageSize = new Label();
            cmbPageSize = new ComboBox();
            btnColumnsVisibility = new Button();
            tlpMain.SuspendLayout();
            ((ISupportInitialize)dgvMain).BeginInit();
            SuspendLayout();
            // 
            // tlpMain
            // 
            tlpMain.BackColor = Color.FromArgb(234, 242, 248);
            tlpMain.ColumnCount = 5;
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20F));
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20F));
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20F));
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20F));
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20F));
            tlpMain.Controls.Add(dgvMain, 0, 1);
            tlpMain.Controls.Add(btnFirst, 0, 0);
            tlpMain.Controls.Add(btnPrevious, 1, 0);
            tlpMain.Controls.Add(lblHeader, 2, 0);
            tlpMain.Controls.Add(btnLast, 4, 0);
            tlpMain.Controls.Add(btnNext, 3, 0);
            tlpMain.Controls.Add(lblPageSize, 0, 2);
            tlpMain.Controls.Add(btnColumnsVisibility, 4, 2);
            tlpMain.Controls.Add(cmbPageSize, 1, 2);
            tlpMain.Dock = DockStyle.Fill;
            tlpMain.ForeColor = Color.FromArgb(22, 71, 117);
            tlpMain.Location = new Point(0, 0);
            tlpMain.Margin = new Padding(4, 3, 4, 3);
            tlpMain.Name = "tlpMain";
            tlpMain.RightToLeft = RightToLeft.No;
            tlpMain.RowCount = 3;
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Absolute, 45F));
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Absolute, 40F));
            tlpMain.Size = new Size(600, 400);
            tlpMain.TabIndex = 1;
            // 
            // dgvMain
            // 
            tlpMain.SetColumnSpan(dgvMain, 5);
            dgvMain.DataSource = null;
            dgvMain.Dock = DockStyle.Fill;
            dgvMain.Location = new Point(4, 48);
            dgvMain.Margin = new Padding(4, 3, 4, 3);
            dgvMain.Name = "dgvMain";
            dgvMain.Size = new Size(592, 309);
            dgvMain.SummaryColumns = null;
            dgvMain.TabIndex = 1;
            // 
            // btnFirst
            // 
            btnFirst.Dock = DockStyle.Fill;
            btnFirst.Location = new Point(3, 3);
            btnFirst.Name = "btnFirst";
            btnFirst.Size = new Size(114, 39);
            btnFirst.TabIndex = 5;
            btnFirst.Text = "First";
            btnFirst.UseVisualStyleBackColor = true;
            // 
            // btnPrevious
            // 
            btnPrevious.Dock = DockStyle.Fill;
            btnPrevious.Location = new Point(123, 3);
            btnPrevious.Name = "btnPrevious";
            btnPrevious.Size = new Size(114, 39);
            btnPrevious.TabIndex = 5;
            btnPrevious.Text = "Previous";
            btnPrevious.UseVisualStyleBackColor = true;
            // 
            // lblHeader
            // 
            lblHeader.AutoSize = true;
            lblHeader.Dock = DockStyle.Fill;
            lblHeader.Location = new Point(243, 0);
            lblHeader.Name = "lblHeader";
            lblHeader.Size = new Size(114, 45);
            lblHeader.TabIndex = 8;
            lblHeader.Text = "...";
            lblHeader.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // btnLast
            // 
            btnLast.Dock = DockStyle.Fill;
            btnLast.Location = new Point(483, 3);
            btnLast.Name = "btnLast";
            btnLast.Size = new Size(114, 39);
            btnLast.TabIndex = 5;
            btnLast.Text = "Last";
            btnLast.UseVisualStyleBackColor = true;
            // 
            // btnNext
            // 
            btnNext.Dock = DockStyle.Fill;
            btnNext.Location = new Point(363, 3);
            btnNext.Name = "btnNext";
            btnNext.Size = new Size(114, 39);
            btnNext.TabIndex = 5;
            btnNext.Text = "Next";
            btnNext.UseVisualStyleBackColor = true;
            // 
            // lblPageSize
            // 
            lblPageSize.AutoSize = true;
            lblPageSize.Location = new Point(3, 360);
            lblPageSize.Name = "lblPageSize";
            lblPageSize.Size = new Size(110, 30);
            lblPageSize.TabIndex = 6;
            lblPageSize.Text = "Number Of Results Displayed";
            // 
            // cmbPageSize
            // 
            cmbPageSize.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            cmbPageSize.FormattingEnabled = true;
            cmbPageSize.Items.AddRange(new object[] {
            "15",
            "25",
            "50",
            "100",
            "250",
            "500",
            "1000",
            "5000"});
            cmbPageSize.Location = new Point(123, 368);
            cmbPageSize.Name = "cmbPageSize";
            cmbPageSize.Size = new Size(114, 23);
            cmbPageSize.TabIndex = 7;
            // 
            // btnColumnsVisibility
            // 
            btnColumnsVisibility.Dock = DockStyle.Fill;
            btnColumnsVisibility.Location = new Point(483, 363);
            btnColumnsVisibility.Name = "btnColumnsVisibility";
            btnColumnsVisibility.Size = new Size(114, 34);
            btnColumnsVisibility.TabIndex = 5;
            btnColumnsVisibility.Text = "Columns Visibility";
            btnColumnsVisibility.UseVisualStyleBackColor = true;
            // 
            // PaginationDataGridView
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            Controls.Add(tlpMain);
            Name = "PaginationDataGridView";
            Size = new Size(600, 400);
            tlpMain.ResumeLayout(false);
            tlpMain.PerformLayout();
            ((ISupportInitialize)dgvMain).EndInit();
            ResumeLayout(false);
        }

        #endregion

        private TableLayoutPanel tlpMain;
        private Button btnFirst;
        private Button btnPrevious;
        private Button btnNext;
        private Button btnLast;
        private dgvTotalsSummary dgvMain;
        private Label lblHeader;
        private Label lblPageSize;
        private ComboBox cmbPageSize;
        private Button btnColumnsVisibility;
    }
}
