﻿namespace GMCadiomCore.Shared.Helper
{
    public static class JsonUtilities
    {
        public static void SaveTo<PERSON><PERSON>(object model, string JsonFile)
        {
            try
            {
                var jsonContent = JsonConvert.SerializeObject(model, Formatting.Indented,
                    new JsonSerializerSettings()
                    {
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                    });
                File.WriteAllText(JsonFile, jsonContent);
            }
            catch (Exception ex)
            {
            }
        }

        public static T? ReadFromJson<T>(string JsonFile)
        {
            try
            {
                if (File.Exists(JsonFile))
                {
                    string jsonContent = File.ReadAllText(JsonFile);
                    var model = JsonConvert.DeserializeObject<T>(jsonContent);
                    if (model != null)
                        return model;
                }
            }
            catch (Exception ex)
            {
                return default;
            }
            return default;
        }

        public static async Task SaveToJsonAsync(object model, string JsonFile)
        {
            try
            {
                var jsonContent = JsonConvert.SerializeObject(model, Formatting.Indented,
                    new JsonSerializerSettings()
                    {
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                    });
                await File.WriteAllTextAsync(JsonFile, jsonContent);
            }
            catch (Exception ex)
            {
            }
        }

        public static async Task<T?> ReadFromJsonAsync<T>(string JsonFile)
        {
            try
            {
                if (File.Exists(JsonFile))
                {
                    string jsonContent = await File.ReadAllTextAsync(JsonFile);
                    var model = JsonConvert.DeserializeObject<T>(jsonContent);
                    if (model != null)
                        return model;
                }
            }
            catch (Exception ex)
            {
                return default;
            }
            return default;
        }
    }
}
