﻿namespace GMCadiomCore.Presenters.AutoMapper.BasePresenter
{
    public class BasePresenter<T1, T2> : Presenters.BasePresenter.BasePresenter<T1, T2>, IBasePresenter<T1, T2> where T1 : BaseModel where T2 : BaseModel
    {
        //Fields
        public IMapper Mapper { get; private set; }

        //Constructor
        public BasePresenter(IBaseView<T1> view, IBaseUnitOfWork repository, IBaseService<T1, T2> baseService, IMapper mapper) : base(view, repository, baseService)
        {
            Mapper = mapper;
        }
    }
}
