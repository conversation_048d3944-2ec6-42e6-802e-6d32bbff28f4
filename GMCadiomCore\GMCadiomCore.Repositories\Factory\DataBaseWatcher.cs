﻿namespace GMCadiomCore.Repositories.Factory
{
    public static class DataBaseWatcher
    {
        public delegate void DataBaseWatcherEventHandler(DataBaseEntity dataBaseEntity);
        public static event DataBaseWatcherEventHandler OnDataBaseWatcherChanged;

        public static void NotifyDataBaseWatcherChanged(DataBaseEntity dataBaseEntity)
        {
            if (dataBaseEntity is not null)
                return;

            string dataBaseEntityString = JsonConvert.SerializeObject(dataBaseEntity, Formatting.Indented);

            NotifyDataBaseWatcherChanged(dataBaseEntityString);
        }

        public static void NotifyDataBaseWatcherChanged(string dataBaseEntityString)
        {
            if (string.IsNullOrEmpty(dataBaseEntityString)) return;

            try
            {
                DataBaseEntity? deserialized = JsonConvert.DeserializeObject<DataBaseEntity>(dataBaseEntityString, new JsonSerializerSettings()
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    PreserveReferencesHandling = PreserveReferencesHandling.Objects,
                });

                if (deserialized != null)
                    OnDataBaseWatcherChanged?.Invoke(deserialized);
            }
            catch (Exception ex)
            {
            }
        }
    }
}
