﻿namespace GMCadiomCore.Desktop.Presenters.View
{
    public partial class BaseListView : BaseView, IBaseListView
    {
        #region Fields
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public List<ControlsValueModel> listControlValue { get; set; } = new List<ControlsValueModel>();
        protected Label lblSearchValues = new Label()
        {
            Name = "lblSearchValues",
            Anchor = AnchorStyles.Right | AnchorStyles.Left,
            TextAlign = ContentAlignment.MiddleLeft,
        };
        protected Button btnClearFilter = new Button()
        {
            Name = "btnClearFilter",
            Anchor = AnchorStyles.Right | AnchorStyles.Left,
            Text = "Clear Filter",
        };
        protected TableLayoutPanel tlpFilter = new TableLayoutPanel()
        {
            Dock = DockStyle.Fill,
            RowCount = 1,
            ColumnCount = 2,
        };
        protected bool isSearched { set => SearchingHandler(value); }
        private bool _IsPrintable = false;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsPrintable { get => _IsPrintable; set { _IsPrintable = value; ShowPrintButtons(value); } }
        private bool _IsImportable = true;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsImportable { get => _IsImportable; set { _IsImportable = value; ShowImportButtons(value); } }
        private bool _IsExportable = true;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsExportable { get => _IsExportable; set { _IsExportable = value; ShowExportButtons(value); } }
        private bool _IsListOnly = true;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsListOnly { get => _IsListOnly; set { _IsListOnly = value; IsListOnlyControls(value); } }
        public DialogResult result;
        #endregion

        public BaseListView()
        {
            InitializeComponent();
            tlpFilter.ColumnStyles.Add(new ColumnStyle() { Width = 100, SizeType = SizeType.Percent });
            tlpFilter.ColumnStyles.Add(new ColumnStyle() { Width = 100, SizeType = SizeType.Absolute });
            tlpFilter.Controls.Add(lblSearchValues, 0, 0);
            tlpFilter.Controls.Add(btnClearFilter, 1, 0);
            AssociateAndRaiseEvents();
        }

        private void AssociateAndRaiseEvents()
        {
            this.Load += (s, e) =>
            {
                ShowPrintButtons(IsPrintable);
                ShowImportButtons(IsImportable);
                ShowExportButtons(IsExportable);
            };
        }

        public virtual void ShowPrintButtons(bool value)
        {
        }

        public virtual void ShowImportButtons(bool value)
        {
        }

        public virtual void ShowExportButtons(bool value)
        {
        }

        public virtual void IsListOnlyControls(bool value)
        {
        }

        public virtual void SearchingHandler(bool value)
        {
            //if (value)
            //{
            //    lblSearchValues.Text = GetListControlsValues(listControlValue);
            //    if (tlpMain.RowCount == 1)
            //    {
            //        tlpMain.RowCount = 2;
            //        tlpMain.RowStyles.Add(new RowStyle() { Height = 35, SizeType = SizeType.Absolute });
            //        tlpMain.Controls.Add(lblSearchValues, 0, 1);
            //        tlpMain.Size = new System.Drawing.Size(tlpMain.Size.Width, 55 + 35);
            //    }
            //}
            //else
            //{
            //    if (tlpMain.RowCount == 2)
            //    {
            //        Utilities.RemoveArbitraryRow(tlpMain, 1);
            //        listControlValue = new BindingList<ControlsValueModel>();
            //        tlpMain.Size = new System.Drawing.Size(tlpMain.Size.Width, 55);
            //    }
            //}
        }

        public virtual void Clear()
        {
        }

        #region Buttons Actions Events
        public virtual void NewFunction()
        {
            isSearched = false;
            AddNewEvent?.Invoke(this, EventArgs.Empty);
        }
        public virtual void EditFunction()
        {
            if (BaseSession.CheckActionAuthorization(this.Name, Actions.Edit))
            {
                isSearched = false;
                EditEvent?.Invoke(this, EventArgs.Empty);
            }
        }
        public virtual void DeleteFunction()
        {
            if (BaseSession.CheckActionAuthorization(this.Name, Actions.Delete))
            {
                isSearched = false;
                var result = MessageBox.Show("هل تريد حذف البيانات ؟", "تنبيه", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                if (result == DialogResult.Yes)
                    DeleteEvent?.Invoke(this, EventArgs.Empty);
            }
        }
        public virtual void PrintFunction()
        {
            if (BaseSession.CheckActionAuthorization(this.Name, Actions.Print))
                PrintFunction();
        }
        public virtual void RefreshListFunction()
        {
            RefreshEvent?.Invoke(this, EventArgs.Empty);
        }
        public virtual void GenerateFunction() { }
        public virtual void ImportFunction()
        {
            isSearched = false;
            OpenFileDialog openFileDialog = new OpenFileDialog();
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                importPath = openFileDialog.FileNames[0];
                ImportEvent?.Invoke(this, EventArgs.Empty);
            }
        }
        public virtual void ExportFunction()
        {
            ExportEvent?.Invoke(this, EventArgs.Empty);
        }
        public virtual void FilterFunction()
        {
            isSearched = true;
            SearchEvent?.Invoke(this, EventArgs.Empty);
        }
        public virtual void ClearFilterFunction()
        {
            isSearched = false;
            ClearSearchEvent?.Invoke(this, EventArgs.Empty);
        }
        #endregion

        #region Filter Action Events
        public object? GetSearchValue([CallerMemberName] string name = "")
        {
            var value = listControlValue.FirstOrDefault(x => x.labelName == name);
            if (value != null)
                return value.valueControl;
            else
                return null;
        }
        protected string GetListControlsValues(List<ControlsValueModel> listControlValue)
        {
            string stringValue = "";

            listControlValue.ForEach(x =>
            {
                if (!string.IsNullOrEmpty(x.valueControl?.ToString()))
                {
                    var value = x.valueControl.GetType() == typeof(DateTime) ? ValidateValue.ValidateDateTime(x.valueControl).Date.ToShortDateString() : x.valueControl;

                    stringValue += $"{x.labelText} : {value}";

                    if (listControlValue.IndexOf(x) != listControlValue.Count - 1)
                        stringValue += $" | ";
                }
            });

            return stringValue;
        }
        #endregion

        #region Implement IBaseListView Interface
        //Properties
        private string importPath;
        public string ImportPath { get => importPath; }
        public string Message { set => MessageBox.Show(value); }
        public bool DataHasChanged { set => HandleDataHasChanged(value); }
        public virtual void HandleDataHasChanged(bool value)
        {
            //this.SafelyInvokeAction(async () =>
            //{
            //    Color baseColor = tsbtnRefreshList.BackColor;
            //    // Flashing effect to indicate update
            //    for (int i = 0; i < 20; i++)
            //    {
            //        tsbtnRefreshList.BackColor = i % 2 == 0 ? Color.Yellow : Color.Orange;
            //        await Task.Delay(500);  // Pause for 500 milliseconds
            //    }
            //    tsbtnRefreshList.BackColor = baseColor; // Reset to default
            //});
        }

        //Events
        public event EventHandler AddNewEvent;
        public event EventHandler EditEvent;
        public event EventHandler DeleteEvent;
        public event EventHandler RefreshEvent;
        public event EventHandler SearchEvent;
        public event EventHandler ImportEvent;
        public event EventHandler ExportEvent;
        public event EventHandler ClearSearchEvent;
        #endregion
    }
}
