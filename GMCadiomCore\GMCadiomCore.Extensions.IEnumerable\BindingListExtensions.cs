﻿namespace GMCadiomCore.Extensions.IEnumerable
{
    public static class BindingListExtensions
    {
        public static BindingList<T> GetNewBindable<T>(this BindingList<T> source, Func<T, bool> predicate, string keyValue)
        {
            if (source == null) return new BindingList<T>();
            List<T> filteredList = source.Where(predicate).ToList();
            BindingList<T> newList = new BindingList<T>(filteredList);
            source.ListChanged += (s, e) => Source_ListChanged(s, e, source, newList, keyValue);
            return newList;
        }

        public static BindingList<T> Paganate<T>(this BindingList<T> source, int skip, int take, string keyValue)
        {
            if (source == null) return new BindingList<T>();
            List<T> filteredList = source.Skip(skip).Take(take).ToList();
            BindingList<T> newList = new BindingList<T>(filteredList);
            source.ListChanged += (s, e) => Source_ListChanged(s, e, source, newList, keyValue);
            return newList;
        }

        private static void Source_ListChanged<T>(object? sender, ListChangedEventArgs listChanged, BindingList<T> source, BindingList<T> newList, string keyValue)
        {
            T item = source[listChanged.NewIndex];
            if (item != null)
            {
                switch (listChanged.ListChangedType)
                {
                    case ListChangedType.Reset:
                        break;
                    case ListChangedType.ItemAdded:
                        newList.Add(item);
                        break;
                    case ListChangedType.ItemDeleted:
                        newList.Remove(item);
                        break;
                    case ListChangedType.ItemMoved:
                        break;
                    case ListChangedType.ItemChanged:
                        if (!string.IsNullOrEmpty(keyValue))
                        {
                            Type type = typeof(T);
                            PropertyInfo? propertyInfo = type.GetProperties().Where(x => x.Name == keyValue).FirstOrDefault();
                            if (propertyInfo != null)
                            {
                                var value = propertyInfo.GetValue(item);
                                if (value != null)
                                {
                                    ParameterExpression parameterExpression = Expression.Parameter(typeof(T), nameof(T));
                                    MemberExpression memberExpression = Expression.Property(parameterExpression, propertyInfo.Name);
                                    ConstantExpression constantExpression = Expression.Constant(value, typeof(int));
                                    BinaryExpression equalBinaryExpression = Expression.Equal(memberExpression, constantExpression);
                                    Func<T, bool> isEqualExpression = Expression.Lambda<Func<T, bool>>(equalBinaryExpression, parameterExpression).Compile();

                                    T? oldItem = newList.SingleOrDefault(isEqualExpression);
                                    if (oldItem != null)
                                    {
                                        int index = newList.IndexOf(oldItem);
                                        newList[index] = item;
                                    }
                                    else
                                    {

                                    }
                                }
                            }
                        }
                        break;
                    case ListChangedType.PropertyDescriptorAdded:
                        break;
                    case ListChangedType.PropertyDescriptorDeleted:
                        break;
                    case ListChangedType.PropertyDescriptorChanged:
                        break;
                }
            }
        }
    }
}
