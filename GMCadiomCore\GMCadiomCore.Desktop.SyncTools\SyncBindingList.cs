﻿namespace GMCadiomCore.Desktop.SyncTools
{
    public class SyncBindingList<T> : BindingList<T>
    {
        private SynchronizationContext? syncContext;
        public SyncBindingList()
        {
            syncContext = SynchronizationContext.Current;
        }
        public SyncBindingList(IList<T> items)
        {
            syncContext = SynchronizationContext.Current;
            DataSource = items;
        }
        protected override void OnListChanged(ListChangedEventArgs e)
        {
            if (syncContext != null)
                syncContext.Send(_ => base.OnListChanged(e), null);
            else
                base.OnListChanged(e);
        }
        protected override void OnAddingNew(AddingNewEventArgs e)
        {
            if (syncContext != null)
                syncContext.Send(_ => base.OnAddingNew(e), null);
            else
                base.OnAddingNew(e);
        }
        protected override void InsertItem(int index, T item)
        {

            if (syncContext != null)
                syncContext.Send(_ => base.InsertItem(index, item), null);
            else
                base.InsertItem(index, item);
        }
        protected override void SetItem(int index, T item)
        {
            if (syncContext != null)
                syncContext.Send(_ => base.SetItem(index, item), null);
            else
                base.SetItem(index, item);
        }
        protected override void RemoveItem(int index)
        {
            if (syncContext != null)
                syncContext.Send(_ => base.RemoveItem(index), null);
            else
                base.RemoveItem(index);
        }
        protected override void ClearItems()
        {
            if (syncContext != null)
                syncContext.Send(_ => base.ClearItems(), null);
            else
                base.ClearItems();
        }
        public override void CancelNew(int itemIndex)
        {
            if (syncContext != null)
                syncContext.Send(_ => base.CancelNew(itemIndex), null);
            else
                base.CancelNew(itemIndex);
        }
        protected override void ApplySortCore(PropertyDescriptor prop, ListSortDirection direction)
        {
            if (syncContext != null)
                syncContext.Send(_ => base.ApplySortCore(prop, direction), null);
            else
                base.ApplySortCore(prop, direction);
        }
        protected override void RemoveSortCore()
        {
            if (syncContext != null)
                syncContext.Send(_ => base.RemoveSortCore(), null);
            else
                base.RemoveSortCore();
        }
        public override void EndNew(int itemIndex)
        {
            if (syncContext != null)
                syncContext.Send(_ => base.EndNew(itemIndex), null);
            else
                base.EndNew(itemIndex);
        }
        public IList<T> DataSource
        {
            get
            {
                return this;
            }
            set
            {
                if (value != null)
                {
                    ClearItems();
                    RaiseListChangedEvents = false;
                    foreach (T item in value)
                        Add(item);
                    RaiseListChangedEvents = true;
                    OnListChanged(new ListChangedEventArgs(ListChangedType.Reset, -1));
                }
            }
        }
    }
}