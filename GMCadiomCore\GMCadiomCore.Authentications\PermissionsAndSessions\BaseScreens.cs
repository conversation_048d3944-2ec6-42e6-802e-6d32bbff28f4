﻿namespace GMCadiomCore.Authentications.PermissionsAndSessions
{
    public class BaseScreens : BaseScreensAccessTemplate
    {
        public BaseScreens(Ulid screenId, BaseScreensAccessTemplate? parent = null, string? name = null) : base(screenId, parent, name)
        {

        }

        private static List<BaseScreensAccessTemplate> ListOfScreens = new List<BaseScreensAccessTemplate>();

        public static void AddScreen(BaseScreensAccessTemplate screen) => ListOfScreens.Add(screen);
        public static void AddScreen(List<BaseScreensAccessTemplate> screens) => ListOfScreens.AddRange(screens);

        public static List<BaseScreensAccessTemplate> GetScreens
        {
            get
            {
                List<BaseScreensAccessTemplate> list = new List<BaseScreensAccessTemplate>();

                ListOfScreens.ForEach(x =>
                {
                    list.Add(new BaseScreensAccessTemplate()
                    {
                        Id = x.Id,
                        ScreenId = x.ScreenId,
                        ScreenName = x.ScreenName,
                        ScreenText = x.ScreenText,
                        CanShow = x.CanShow,
                        CanOpen = x.Can<PERSON>pen,
                        CanAdd = x.CanAdd,
                        CanEdit = x.CanEdit,
                        CanDelete = x.CanDelete,
                        CanPrint = x.CanPrint,
                        HasChild = x.HasChild,
                        ParentScreenId = x.ParentScreenId,
                        Actions = x.Actions,
                    });
                });
                return list;
            }
        }
    }
}
