﻿namespace GMCadiomCore.Desktop.CustomEvents
{
    public static class TextBoxEvents
    {
        public static void NumbersOnly(object? sender, KeyPressEventArgs e)
        {
            if (!char.<PERSON>ontrol(e.Key<PERSON>har) && !char.IsDigit(e.<PERSON>har) && e.<PERSON>har != '.')
            {
                e.Handled = true;
            }

            // only allow one decimal point
            if (e.KeyChar == '.' && sender is TextBox textBox && textBox.Text.IndexOf('.') > -1)
            {
                e.Handled = true;
            }
        }

        public static void SetDecimalView(object? sender, EventArgs e)
        {
            if (sender is TextBox textBox)
                textBox.Text = ValidateValue.ValidateDecimal(textBox.Text).ToString();
        }

        public static void PhoneNumbersOnly(object? sender, KeyPressEventArgs e)
        {
            if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && e.Key<PERSON>har != '+')
            {
                e.Handled = true;
            }

            // only allow one decimal point
            if (e.KeyChar == '+' && sender is TextBox textBox && textBox.Text.IndexOf('+') > -1)
            {
                e.Handled = true;
            }
        }

        public static void EmailOnly(object? sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == '@' && sender is TextBox textBox && textBox.Text.IndexOf('@') > -1)
            {
                e.Handled = true;
            }
        }

        public static void IsEmailValid(object? sender, CancelEventArgs e)
        {
            if (sender is TextBox textBox && ValidateValue.EmailValidation(textBox.Text) != true)
                e.Cancel = true;
        }
    }
}
