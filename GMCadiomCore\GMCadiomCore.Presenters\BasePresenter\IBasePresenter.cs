﻿namespace GMCadiomCore.Presenters.BasePresenter
{
    public interface IBasePresenter<T1, T2> where T1 : BaseModel where T2 : BaseModel
    {
        bool IsDataChanged { get; set; }
        IBaseView<T1> View { get; }
        IBaseUnitOfWork Repository { get; }
        IBaseService<T1, T2> BaseService { get; }
        T1 CurrentModel { get; set; }
        BindingList<T1>? TempList { get; set; }
        string TempListPath { get; }
        void LoadAllLists();
        void AddNewAction();
        void DeleteSelectedAction();
        void GetCurrentModelToJSON();
        void SetCurrentModelFromJSON(T1 Model);
        void SaveTempAction();
        void SaveAction();
        void CleanViewFieldsAction();
    }
}
