﻿namespace GMCadiomCore.Desktop.Shared.UI
{
    internal static class ColorsSchema
    {
        static IColorsPlate colorsPlate = new ColorsPlate1();

        internal static RightToLeft IsRTL { get => RightToLeft.No; }
        internal static bool IsRTLLayout { get => false; }

        internal static class Button
        {
            internal static Color BackColor { get => colorsPlate.Level2; }
            internal static Color ForeColor { get => colorsPlate.Level4; }
        }

        internal static class CheckBox
        {
            internal static Color BackColor { get => colorsPlate.Level1; }
            internal static Color ForeColor { get => colorsPlate.Level4; }
        }

        internal static class ComboBox
        {
            internal static Color BackColor { get => colorsPlate.Level1; }
            internal static Color ForeColor { get => colorsPlate.Level4; }

        }

        internal static class DataGridView
        {
            internal static Color BackColor => colorsPlate.Level1;
            internal static Color ForeColor => colorsPlate.Level4;
            internal static Color BackgroundColor => colorsPlate.Level1;

            internal static Color AlternatingRowsDefaultCellStyleBackColor => colorsPlate.Level1;
            internal static Color AlternatingRowsDefaultCellStyleForeColor => colorsPlate.Level4;
            internal static Color AlternatingRowsDefaultCellStyleSelectionBackColor => colorsPlate.Level2;
            internal static Color AlternatingRowsDefaultCellStyleSelectionForeColor => colorsPlate.Level4;

            internal static Color ColumnHeadersDefaultCellStyleBackColor => colorsPlate.Level1;
            internal static Color ColumnHeadersDefaultCellStyleForeColor => colorsPlate.Level4;
            internal static Color ColumnHeadersDefaultCellStyleSelectionBackColor => colorsPlate.Level2;
            internal static Color ColumnHeadersDefaultCellStyleSelectionForeColor => colorsPlate.Level4;

            internal static Color DefaultCellStyleBackColor => colorsPlate.Level1;
            internal static Color DefaultCellStyleForeColor => colorsPlate.Level4;
            internal static Color DefaultCellStyleSelectionBackColor => colorsPlate.Level2;
            internal static Color DefaultCellStyleSelectionForeColor => colorsPlate.Level4;
        }

        internal static class DateTimePicker
        {
            internal static Color BackColor { get => colorsPlate.Level1; }
            internal static Color ForeColor { get => colorsPlate.Level4; }
        }

        internal static class Form
        {
            internal static Color BackColor { get => colorsPlate.Level1; }
            internal static Color ForeColor { get => colorsPlate.Level4; }
        }

        internal static class GroupBox
        {
            internal static Color BackColor { get => colorsPlate.Level1; }
            internal static Color ForeColor { get => colorsPlate.Level4; }
        }

        internal static class Label
        {
            internal static Color BackColor { get => colorsPlate.Level1; }
            internal static Color ForeColor { get => colorsPlate.Level4; }
        }

        internal static class RadioButton
        {
            internal static Color BackColor { get => colorsPlate.Level1; }
            internal static Color ForeColor { get => colorsPlate.Level4; }
        }

        internal static class StatusStrip
        {
            internal static Color BackColor { get => colorsPlate.Level3; }
            internal static Color ForeColor { get => colorsPlate.Level1; }
        }

        internal static class TabControl
        {
            internal static Color BackColor { get => colorsPlate.Level1; }
            internal static Color ForeColor { get => colorsPlate.Level4; }
        }

        internal static class TableLayoutPanel
        {
            internal static Color BackColor { get => colorsPlate.Level1; }
            internal static Color ForeColor { get => colorsPlate.Level4; }
        }

        internal static class TabPage
        {
            internal static Color BackColor { get => colorsPlate.Level1; }
            internal static Color ForeColor { get => colorsPlate.Level4; }
        }

        internal static class TextBox
        {
            internal static Color BackColor { get => colorsPlate.Level1; }
            internal static Color ForeColor { get => colorsPlate.Level4; }
        }

        internal static class ToolStrip
        {
            internal static Color BackColor { get => colorsPlate.Level2; }
            internal static Color ForeColor { get => colorsPlate.Level4; }
        }
    }
}
