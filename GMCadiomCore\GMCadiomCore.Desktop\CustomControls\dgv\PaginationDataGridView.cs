﻿namespace GMCadiomCore.Desktop.CustomControls.dgv
{
    public partial class PaginationDataGridView : UserControl, IPaginationList
    {
        private PaginationList? _dataSource;
        private bool _internalUpdate = false;

        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public PaginationList? DataSource { get => _dataSource; set => SetDataSource(value); }

        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string DataSourceName { get; private set; }

        public PaginationDataGridView()
        {
            InitializeComponent();
            cmbPageSize.SelectedItem = "15";

            frmColumnsVisibility = new frmColumnsVisibility();

            AssociateAndRaiseEvents();
        }

        private void AssociateAndRaiseEvents()
        {
            #region Pagination
            cmbPageSize.SelectedIndexChanged += (s, e) => FireRefreshData();
            btnPrevious.Click += (s, e) =>
            {
                if (DataSource != null)
                    if (DataSource.HasPreviousPage)
                    {
                        --PageNumber;
                        Previous?.Invoke(s, e);
                    }
            };
            btnNext.Click += (s, e) =>
            {
                if (DataSource != null)
                    if (DataSource.HasNextPage)
                    {
                        ++PageNumber;
                        Next?.Invoke(s, e);
                    }
            };
            btnFirst.Click += (s, e) =>
            {
                PageNumber = 1;
                First?.Invoke(s, e);
            };
            btnLast.Click += (s, e) =>
            {
                if (DataSource != null)
                    PageNumber = DataSource.TotalPages;
                Last?.Invoke(s, e);
            };
            #endregion

            #region Column Visibility
            btnColumnsVisibility.Click += (s, e) => frmColumnsVisibility.ShowDialog();
            frmColumnsVisibility.CheckedItemsChanged += checkedComboBox_CheckedItemsChanged;
            #endregion
        }

        private void SetDataSource(PaginationList? paginationList)
        {
            if (paginationList == null || paginationList.DataSource == null)
                return;

            this.SafelyInvokeAction(() =>
            {
                btnPrevious.Enabled = paginationList.HasPreviousPage;
                btnNext.Enabled = paginationList.HasNextPage;

                _dataSource = paginationList;
                dgvMain.DataSource = paginationList.DataSource;
                PageNumber = paginationList.PageNumber;

                Type listType = paginationList.DataSource.GetType();
                Type[] genericArguments = listType.GetGenericArguments();
                Type typeArgument = genericArguments[0];
                DataSourceName = typeArgument.Name;

                lblHeader.Text = $"Page {PageNumber} / {(paginationList.TotalPages < 1 ? 1 : paginationList.TotalPages)}";

                LoadLoadedItems();
            });
        }

        private void FireRefreshData()
        {
            if (_internalUpdate)
                return;
            PageNumber = 1;
            RefreshData?.Invoke(this, EventArgs.Empty);
        }

        public dgvTotalsSummary GetDgvTotalsSummary => dgvMain;

        #region Implement IPaginationList Interface
        // Properties
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string Header { get; set; }
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public int PageSize
        {
            get
            {
                int pageSize = 0;
                this.SafelyInvokeAction(() =>
                {
                    pageSize = ValidateValue.ValidateInt(cmbPageSize.SelectedItem);
                    if (pageSize == 0)
                    {
                        _internalUpdate = true;
                        pageSize = 15;
                        cmbPageSize.SelectedItem = "15";
                        _internalUpdate = false;
                    }
                });
                return pageSize;
            }
        }
        private int _pageNumber = 1;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public int PageNumber { get => _pageNumber; set => _pageNumber = value; }

        // Events
        public event EventHandler RefreshData;
        public event EventHandler Previous;
        public event EventHandler Next;
        public event EventHandler First;
        public event EventHandler Last;
        #endregion

        #region Column Visibility
        private frmColumnsVisibility frmColumnsVisibility;
        private string path => SessionPaths.SessionDataGridViewConfigurationsPath();
        private string configPath => Path.Combine(path, DataSourceName);
        protected List<ColumnVisibility> LoadedItems { get; private set; }

        private void LoadLoadedItems()
        {
            LoadedItems = JsonUtilities.ReadFromJson<List<ColumnVisibility>>(configPath) ?? new List<ColumnVisibility>();
            if (LoadedItems != null)
                LoadedItems.ForEach(columnVisibility => frmColumnsVisibility.AddItem(columnVisibility));

            foreach (DataGridViewColumn column in dgvMain.DGV.Columns)
            {
                ColumnVisibility columnVisibility = new ColumnVisibility()
                {
                    ColumnHeaderText = column.HeaderText,
                    ColumnName = column.Name,
                    IsVisible = column.Visible
                };
                frmColumnsVisibility.AddItem(columnVisibility);
                if (column is DataGridViewImageColumn imageColumn)
                    imageColumn.ImageLayout = DataGridViewImageCellLayout.Zoom;
            }
        }
        private void checkedComboBox_CheckedItemsChanged(ColumnVisibility columnVisibility)
        {
            if (columnVisibility == null)
                return;
            var column = dgvMain.DGV.Columns.OfType<DataGridViewColumn>().Where(x => x.Name.Equals(columnVisibility.ColumnName)).ToList();
            if (column.Count > 0)
                dgvMain.DGV.Columns[column[0].Name].Visible = columnVisibility.IsVisible;
            JsonUtilities.SaveToJson(frmColumnsVisibility.Items, configPath);
        }
        #endregion
    }
}
