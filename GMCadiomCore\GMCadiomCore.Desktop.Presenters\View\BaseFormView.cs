﻿namespace GMCadiomCore.Desktop.Presenters.View
{
    public partial class BaseFormView : BaseView, IBaseView
    {
        #region Fields
        private bool _IsNewable = true;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsNewable { get => _IsNewable; set { _IsNewable = value; ShowNewButtons(value); } }
        private bool _IsDeleteable = true;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsDeleteable { get => _IsDeleteable; set { _IsDeleteable = value; ShowDeleteButtons(value); } }
        private bool _IsPrintable = false;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsPrintable { get => _IsPrintable; set { _IsPrintable = value; ShowPrintButtons(value); } }
        private bool _TempSaveable = false;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool TempSaveable { get => _TempSaveable; set { _TempSaveable = value; ShowTempSaveButtons(value); } }
        private bool isFormLoaded = false;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsFormLoaded { get => isFormLoaded; set => isFormLoaded = value; }
        #endregion

        public BaseFormView()
        {
            InitializeComponent();

            AssociateAndRaiseEvents();
        }

        private void AssociateAndRaiseEvents()
        {
            this.Load += (s, e) =>
            {
                ShowNewButtons(IsNewable);
                ShowDeleteButtons(IsDeleteable);
                ShowPrintButtons(IsPrintable);
                ShowTempSaveButtons(TempSaveable);
            };
            this.Shown += (s, e) => IsFormLoaded = true;
        }

        public virtual void ShowNewButtons(bool value)
        {
        }

        public virtual void ShowSaveButtons(bool value)
        {
        }

        public virtual void ShowDeleteButtons(bool value)
        {
        }

        public virtual void ShowPrintButtons(bool value)
        {
        }

        public virtual void ShowTempSaveButtons(bool value)
        {
        }

        #region Buttons Actions Events
        public virtual void NewFunction()
        {
            this.Focus();
            AddNewEvent?.Invoke(this, EventArgs.Empty);
        }
        public virtual void SaveFunction()
        {
            this.Focus();
            if (BaseSession.CheckActionAuthorization(this.Name, isEdit ? Actions.Edit : Actions.Add))
            {
                SaveEvent?.Invoke(this, EventArgs.Empty);
                if (IsSuccessful)
                    NewFunction();
            }
        }
        public virtual void SaveAndPrintFunction()
        {
            this.Focus();
            if (BaseSession.CheckActionAuthorization(this.Name, isEdit ? Actions.Edit : Actions.Add) && BaseSession.CheckActionAuthorization(this.Name, Actions.Print))
            {
                {
                    SaveEvent?.Invoke(this, EventArgs.Empty);
                    if (IsSuccessful)
                    {
                        if (IsPrintable)
                            PrintFunction();
                        NewFunction();
                    }
                }
            }
        }
        public virtual void TempSaveFunction()
        {
            this.Focus();
            SaveTempEvent?.Invoke(this, EventArgs.Empty);
        }
        public virtual void ShowTempListFunction()
        {
            this.Focus();
            BaseView TempListView = new BaseView();
            DataGridView dgvTempList = new DataGridView()
            {
                Dock = DockStyle.Fill,
                DataSource = TempList,
            };
            dgvTempList.CellDoubleClick += (s, e) =>
            {
                if (e.ColumnIndex > -1 && e.RowIndex > -1)
                    TempListView.DialogResult = DialogResult.OK;
            };
            TempListView.Controls.Add(dgvTempList);
            if (TempListView.ShowDialog() == DialogResult.OK)
            {
                GetTempEvent?.Invoke(this, EventArgs.Empty);
            }
        }
        public virtual void DeleteFunction()
        {
            this.Focus();
            if (BaseSession.CheckActionAuthorization(this.Name, Actions.Delete))
            {
                var result = MessageBox.Show("هل تريد حذف البيانات ؟", "تنبيه", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                if (result == DialogResult.Yes)
                    DeleteEvent?.Invoke(this, EventArgs.Empty);
            }
        }
        public virtual void PrintFunction()
        {
            this.Focus();
            if (BaseSession.CheckActionAuthorization(this.Name, Actions.Print))
                PrintFunction();
        }
        public virtual void IsEditFunction(bool value) { }
        #endregion

        #region Implement IBaseView Interface
        //Properties
        private Ulid id = Ulid.Empty;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public Ulid Id { get => id; set => id = value; }
        private bool isEdit = false;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsEdit
        {
            get => isEdit;
            set
            {
                isEdit = value;
                IsEditFunction(value);
                if (value)
                {
                    if (DocumentTransactionDate.Date.AddDays(2) < DateTime.Now.Date)
                        ShowSaveButtons(false);
                    else
                        ShowSaveButtons(true);
                }
            }
        }
        private bool isSuccessful = false;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsSuccessful { get => isSuccessful; set => isSuccessful = value; }
        public string Message { set => MessageBox.Show(value); }
        private BindingSource _TempList;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public BindingSource TempList { get => _TempList; set => _TempList = value; }

        //Events
        public event EventHandler AddNewEvent;
        public event EventHandler SaveEvent;
        public event EventHandler DeleteEvent;
        public event EventHandler SaveTempEvent;
        public event EventHandler GetTempEvent;
        #endregion
    }
}
