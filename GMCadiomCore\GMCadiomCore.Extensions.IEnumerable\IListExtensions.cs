﻿namespace GMCadiomCore.Extensions.IEnumerable
{
    public static class IListExtensions
    {
        public static IList<T> Clone<T>(this IList<T> listToClone) where T : ICloneable
        {
            return listToClone.Select(item => (T)item.Clone()).ToList();
        }

        public static List<T> Filter<T>(this List<T> collection, string property, object? filterValue)
        {
            if (collection == null || string.IsNullOrWhiteSpace(property) || filterValue == null)
                return new List<T>();

            var filteredCollection = new List<T>();

            foreach (var item in collection)
            {
                if (item == null) continue;

                var propertyInfo = item.GetType().GetProperty(property, BindingFlags.Public | BindingFlags.Instance);

                if (propertyInfo == null)
                    throw new NotSupportedException($"Property '{property}' does not exist on type {typeof(T).Name}.");

                var propertyValue = propertyInfo.GetValue(item, null);
                if (propertyValue == null) continue;

                if (ValidateValue.ValuesAreEqual(propertyValue, filterValue))
                    filteredCollection.Add(item);
            }

            return filteredCollection;
        }

        public static List<T> ToList<T>(this object DataSource)
        {
            switch (DataSource)
            {
                case SyncBindingList<T> syncBindingList:
                    return new List<T>(syncBindingList);
                case BindingList<T> bindingList:
                    return new List<T>(bindingList);
                case List<T> list:
                    return new List<T>(list);
                case IList<T> iList:
                    return new List<T>(iList);
                case ICollection<T> iCollection:
                    return new List<T>(iCollection);
                case IEnumerable<T> iEnumerable:
                    return new List<T>(iEnumerable);
                default:
                    return new List<T>();
            }
        }

        public static List<List<T>> ChunkBy<T, TKey>(this IEnumerable<T> source, Func<T, TKey> keySelector)
        {
            List<List<T>> newList = new List<List<T>>();

            if (source == null)
                throw new NullReferenceException();

            if (source.Count() < 1)
                return newList;

            List<List<T>> chunkedList = source
                .GroupBy(keySelector)
                .Select(group => group.ToList())
                .ToList();

            return newList;
        }

        public static void ForEach<T>(this IList<T> list, Action<T> action)
        {
            if (list is null) return;
            if (action is null) return;

            for (int i = 0; i < list.Count; i++)
                action(list[i]);
        }

        public static void ForEach<T>(this ICollection<T> collection, Action<T> action)
        {
            if (collection == null) return;
            if (action == null) return;

            foreach (var item in collection)
                action(item);
        }

        public static int FindIndex<T>(this IList<T> list, Predicate<T> match)
            => list.FindIndex(0, list.Count, match);

        public static int FindIndex<T>(this IList<T> list, int startIndex, Predicate<T> match)
            => list.FindIndex(startIndex, list.Count - startIndex, match);

        public static int FindIndex<T>(this IList<T> list, int startIndex, int count, Predicate<T> match)
        {
            if (list is null) return -1;
            if (match is null) return -1;

            int endIndex = startIndex + count;
            for (int i = startIndex; i < endIndex; i++)
            {
                if (match(list[i])) return i;
            }
            return -1;
        }

        public static BindingList<T> ToBindingList<T>(this IList<T> list)
        {
            if (list is null)
                return new BindingList<T>();
            else
                return new BindingList<T>(list);
        }

        public static BindingList<T> ToBindingList<T>(this IEnumerable<T> list)
        {
            if (list is null)
                return new BindingList<T>();
            else
                return list.ToList().ToBindingList();
        }

        public static async Task<BindingList<T>> ToBindingListAsync<T>(this IEnumerable<T> list)
        {
            if (list is null)
                return await Task.FromResult(new BindingList<T>());
            else
                return await Task.FromResult(list.ToList().ToBindingList());
        }

        public static void AddRange<T>(this IList<T> list, IEnumerable<T> range)
        {
            if (list is null) return;
            if (range is null) return;

            foreach (T item in range)
                list.Add(item);
        }
    }
}
