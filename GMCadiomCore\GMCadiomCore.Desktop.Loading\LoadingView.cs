﻿using ThreadingTimer = System.Threading.Timer;
using UITimer = System.Windows.Forms.Timer;

namespace GMCadiomCore.Desktop.Loading
{
    public partial class LoadingView : Form
    {
        /// <summary>
        /// Constructor
        /// </summary>
        public LoadingView()
        {
            InitializeComponent();
            SetStyle(
              ControlStyles.AllPaintingInWmPaint |
              ControlStyles.UserPaint |
              ControlStyles.OptimizedDoubleBuffer,
              true);
            //Initialize drawing timer
            _tmrGraphics = new UITimer { Interval = 1 };
            //Invalidate() forces redrawing, and drawing operations are implemented in OnPaint
            _tmrGraphics.Tick += (sender, e) => PnlImage.Invalidate(false);
            _dotSize = PnlImage.Width / 10f;
            //Initialization "point"
            _dots = new LoadingDot[5];
            Color = Color.Orange;
        }

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="message"></param>
        public LoadingView(string message)
        {
            InitializeComponent();
            //Double buffering, no background erasure
            SetStyle(
                ControlStyles.AllPaintingInWmPaint |
                ControlStyles.UserPaint |
                ControlStyles.OptimizedDoubleBuffer,
                true);
            //Initialize drawing timer
            _tmrGraphics = new UITimer { Interval = 1 };
            //Invalidate() forces redrawing, and drawing operations are implemented in OnPaint
            _tmrGraphics.Tick += (sender, e) => PnlImage.Invalidate(false);
            _dotSize = PnlImage.Width / 10f;
            //Initialize "point"
            _dots = new LoadingDot[5];
            Color = Color.Orange;
            Message = message;
        }

        private void FrmLoading_Load(object sender, EventArgs e)
        {
            LblMessage.ForeColor = Color;
            if (Owner != null)
            {
                StartPosition = FormStartPosition.Manual;
                Location = new Point(Owner.Left, Owner.Top);
                Width = Owner.Width;
                Height = Owner.Height;
            }
            else
            {
                Rectangle? screenRect = Screen.PrimaryScreen?.WorkingArea;
                if (screenRect != null)
                    Location = new Point((screenRect.Value.Width - Width) / 2, (screenRect.Value.Height - Height) / 2);
            }
            Start();
        }

        private void FrmLoading_Shown(object sender, EventArgs e)
        {
            if (_workAction != null)
            {
                _workThread = new Thread(ExecWorkAction);
                _workThread.IsBackground = true;
                _workThread.Start();
            }
        }

        #region property  

        [Description("information")]
        public string Message
        {
            get { return LblMessage.Text; }
            set { LblMessage.Text = value; }
        }

        [Browsable(false), Description("Center")]
        public PointF CircleCenter => new PointF(PnlImage.Width / 2f, PnlImage.Height / 2f);

        [Browsable(false), Description("radius")]
        public float CircleRadius => PnlImage.Width / 2f - _dotSize;

        [Browsable(true), Category("Appearance"), Description("Set the foreground color of \"point\"")]
        public Color Color { get; set; }

        #endregion property  

        #region Fields  

        [Description("Is the work completed?")]
        public bool IsWorkCompleted;

        [Description("Working Action")]
        private ParameterizedThreadStart _workAction;

        [Description("Working action parameters")]
        private object _workActionArg;

        [Description("Worker Threads")]
        private Thread _workThread;

        [Description("Abnormal working")]
        public Exception WorkException { get; private set; }

        [Description("Point Array")] private readonly LoadingDot[] _dots;

        [Description("UITimer")] private readonly UITimer _tmrGraphics;

        [Description("ThreadingTimer")] private ThreadingTimer _tmrAction;

        [Description("Point Size")] private float _dotSize;

        [Description("Is it active?")] private bool _isActived;

        [Description("Whether to draw: used to suspend and resume drawing when the state is reset")] private bool _isDrawing = true;

        [Description("Timer count: used to delay the start of each point")] private int _timerCount;

        #endregion Fields  

        #region constant  

        [Description("Action interval (Timer)")] private const int ActionInterval = 30;

        [Description("Counting base: used to calculate the start delay of each point: index * timerCountRadix")] private const int TimerCountRadix = 45;

        #endregion constant  

        #region method  

        /// <summary>
        /// Set up work actions
        /// </summary>
        /// <param name="workAction"></param>
        /// <param name="arg"></param>
        public void SetWorkAction(ParameterizedThreadStart workAction, object arg)
        {
            _workAction = workAction;
            _workActionArg = arg;
        }

        /// <summary>
        /// Execute work actions
        /// </summary>
        private void ExecWorkAction()
        {
            try
            {
                var workTask = new Task(arg =>
                {
                    _workAction(arg);
                }, _workActionArg);
                workTask.Start();
                Task.WaitAll(workTask);
            }
            catch (Exception exception)
            {
                WorkException = exception;
            }
            finally
            {
                IsWorkCompleted = true;
            }
        }

        /// <summary>
        /// Check if reset
        /// </summary>
        /// <returns></returns>
        private bool CheckToReset()
        {
            return _dots.Count(d => d.Opacity > 0) == 0;
        }

        /// <summary>
        /// Initialization point element
        /// </summary>
        private void CreateLoadingDots()
        {
            for (var i = 0; i < _dots.Length; ++i)
                _dots[i] = new LoadingDot(CircleCenter, CircleRadius);
        }

        /// <summary>  
        /// start  
        /// </summary>  
        public void Start()
        {
            CreateLoadingDots();
            _timerCount = 0;
            foreach (var dot in _dots)
            {
                dot.Reset();
            }
            _tmrGraphics.Start();
            //Initialize action timer  
            _tmrAction = new ThreadingTimer(
                state =>
                {
                    //Animation Actions  
                    for (var i = 0; i < _dots.Length; i++)
                    {
                        if (_timerCount++ > i * TimerCountRadix)
                        {
                            _dots[i].LoadingDotAction();
                        }
                    }
                    //Reset  
                    if (CheckToReset())
                    {
                        //Pause drawing before resetting  
                        _isDrawing = false;
                        _timerCount = 0;
                        foreach (var dot in _dots)
                        {
                            dot.Reset();
                        }
                        //Resume drawing  
                        _isDrawing = true;
                    }
                    _tmrAction.Change(ActionInterval, Timeout.Infinite);
                },
                null, ActionInterval, Timeout.Infinite);
            _isActived = true;
        }

        /// <summary>  
        /// stop  
        /// </summary>  
        public void Stop()
        {
            _tmrGraphics.Stop();
            _tmrAction.Dispose();
            _isActived = false;
        }

        #endregion method  

        #region Rewrite  

        protected override void OnPaint(PaintEventArgs e)
        {
            if (IsWorkCompleted)
            {
                Stop();
                Close();
            }
        }

        private void PnlImage_Paint(object sender, PaintEventArgs e)
        {
            if (_isActived && _isDrawing)
            {
                //Anti-Aliasing  
                e.Graphics.SmoothingMode = SmoothingMode.HighQuality;
                using (var bitmap = new Bitmap(200, 200))
                {
                    //Buffered Drawing  
                    using (var bufferGraphics = Graphics.FromImage(bitmap))
                    {
                        //Anti-Aliasing  
                        bufferGraphics.SmoothingMode = SmoothingMode.HighQuality;
                        foreach (var dot in _dots)
                        {
                            var rectangleF = new RectangleF(
                                new PointF(dot.Location.X - _dotSize / 2, dot.Location.Y - _dotSize / 2),
                                new SizeF(_dotSize, _dotSize));
                            bufferGraphics.FillEllipse(new SolidBrush(Color.FromArgb(dot.Opacity, Color)),
                                rectangleF);
                        }
                    }
                    //Textures  
                    e.Graphics.DrawImage(bitmap, new PointF(0, 0));
                } //bmp disposed  
            }
            base.OnPaint(e);
        }

        private void PnlImage_Resize(object sender, EventArgs e)
        {
            PnlImage.Height = PnlImage.Width;
            _dotSize = PnlImage.Width / 12f;
            OnResize(e);
        }

        #endregion Rewrite  
    }
}
