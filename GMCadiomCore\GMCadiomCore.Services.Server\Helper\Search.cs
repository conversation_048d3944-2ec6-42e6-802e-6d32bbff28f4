﻿namespace GMCadiomCore.Services.Server.Helper
{
    public static class Search
    {
        public static Expression<Func<T, bool>> GenerateSearchExpression<T>(T searchValues) where T : class
        {
            ParameterExpression parameterExpression = Expression.Parameter(typeof(T), "x");
            Expression? combinedExpression = null;

            // Get all properties that have the Searchable attribute
            var properties = TypeDescriptor.GetProperties(typeof(T))
                .Cast<PropertyDescriptor>()
                .Where(prop => prop.Attributes[typeof(SearchableAttribute)] != null)
                .ToArray();

            foreach (var property in properties)
            {
                var value = property.GetValue(searchValues);
                if (value != null && !IsDefault(value))
                {
                    MemberExpression memberExpression = Expression.Property(parameterExpression, property.Name);

                    Expression? comparison = null;
                    if (property.PropertyType == typeof(int) || property.PropertyType == typeof(int?))
                    {
                        // Handle int and nullable int
                        int cleanValue = ValidateValue.ValidateInt(value);
                        if (cleanValue > 0)
                        {
                            var constantExpression = Expression.Constant(cleanValue, typeof(int));
                            comparison = property.PropertyType == typeof(int?)
                                ? Expression.AndAlso(
                                    Expression.Property(memberExpression, "HasValue"),
                                    Expression.Equal(Expression.Property(memberExpression, "Value"), constantExpression)
                                  )
                                : Expression.Equal(memberExpression, constantExpression);
                        }
                    }
                    else if (property.PropertyType == typeof(string))
                    {
                        // Handle string
                        string cleanValue = ValidateValue.ValidateString(value);
                        if (!string.IsNullOrEmpty(cleanValue))
                        {
                            var constantExpression = Expression.Constant(cleanValue, typeof(string));
                            var containsMethod = typeof(string).GetMethod("Contains", new[] { typeof(string) });
                            if (containsMethod != null)
                                comparison = Expression.Call(memberExpression, containsMethod, constantExpression);
                        }
                    }
                    // Handle other data types similarly (e.g., decimal, bool, etc.)
                    else if (property.PropertyType == typeof(decimal) || property.PropertyType == typeof(decimal?))
                    {
                        decimal cleanValue = ValidateValue.ValidateDecimal(value);
                        if (cleanValue > 0)
                        {
                            var constantExpression = Expression.Constant(cleanValue, typeof(decimal));
                            comparison = property.PropertyType == typeof(decimal?)
                                ? Expression.AndAlso(
                                    Expression.Property(memberExpression, "HasValue"),
                                    Expression.Equal(Expression.Property(memberExpression, "Value"), constantExpression)
                                  )
                                : Expression.Equal(memberExpression, constantExpression);
                        }
                    }
                    else if (property.PropertyType == typeof(bool) || property.PropertyType == typeof(bool?))
                    {
                        bool cleanValue = ValidateValue.ValidateBool(value);
                        var constantExpression = Expression.Constant(cleanValue, typeof(bool));
                        comparison = property.PropertyType == typeof(bool?)
                            ? Expression.AndAlso(
                                Expression.Property(memberExpression, "HasValue"),
                                Expression.Equal(Expression.Property(memberExpression, "Value"), constantExpression)
                              )
                            : Expression.Equal(memberExpression, constantExpression);
                    }
                    else if (property.PropertyType == typeof(long) || property.PropertyType == typeof(long?))
                    {
                        long cleanValue = ValidateValue.ValidateLong(value);
                        if (cleanValue > 0)
                        {
                            var constantExpression = Expression.Constant(cleanValue, typeof(long));
                            comparison = property.PropertyType == typeof(long?)
                                ? Expression.AndAlso(
                                    Expression.Property(memberExpression, "HasValue"),
                                    Expression.Equal(Expression.Property(memberExpression, "Value"), constantExpression)
                                  )
                                : Expression.Equal(memberExpression, constantExpression);
                        }
                    }
                    else if (property.PropertyType == typeof(short) || property.PropertyType == typeof(short?))
                    {
                        short cleanValue = ValidateValue.ValidateShort(value);
                        if (cleanValue > 0)
                        {
                            var constantExpression = Expression.Constant(cleanValue, typeof(short));
                            comparison = property.PropertyType == typeof(short?)
                                ? Expression.AndAlso(
                                    Expression.Property(memberExpression, "HasValue"),
                                    Expression.Equal(Expression.Property(memberExpression, "Value"), constantExpression)
                                  )
                                : Expression.Equal(memberExpression, constantExpression);
                        }
                    }
                    else if (property.PropertyType == typeof(double) || property.PropertyType == typeof(double?))
                    {
                        double cleanValue = ValidateValue.ValidateDouble(value);
                        if (cleanValue > 0)
                        {
                            var constantExpression = Expression.Constant(cleanValue, typeof(double));
                            comparison = property.PropertyType == typeof(double?)
                                ? Expression.AndAlso(
                                    Expression.Property(memberExpression, "HasValue"),
                                    Expression.Equal(Expression.Property(memberExpression, "Value"), constantExpression)
                                  )
                                : Expression.Equal(memberExpression, constantExpression);
                        }
                    }
                    else if (property.PropertyType == typeof(Ulid) || property.PropertyType == typeof(Ulid?))
                    {
                        Ulid cleanValue = ValidateValue.ValidateUlid(value);
                        if (cleanValue != Ulid.Empty)
                        {
                            var constantExpression = Expression.Constant(cleanValue, typeof(Ulid));
                            comparison = property.PropertyType == typeof(Ulid?)
                                ? Expression.AndAlso(
                                    Expression.Property(memberExpression, "HasValue"),
                                    Expression.Equal(Expression.Property(memberExpression, "Value"), constantExpression)
                                  )
                                : Expression.Equal(memberExpression, constantExpression);
                        }
                    }
                    else if (property.PropertyType == typeof(Guid) || property.PropertyType == typeof(Guid?))
                    {
                        Guid cleanValue = ValidateValue.ValidateGuid(value);
                        if (cleanValue != Guid.Empty)
                        {
                            var constantExpression = Expression.Constant(cleanValue, typeof(Guid));
                            comparison = property.PropertyType == typeof(Guid?)
                                ? Expression.AndAlso(
                                    Expression.Property(memberExpression, "HasValue"),
                                    Expression.Equal(Expression.Property(memberExpression, "Value"), constantExpression)
                                  )
                                : Expression.Equal(memberExpression, constantExpression);
                        }
                    }
                    else if (property.PropertyType == typeof(DateTime) || property.PropertyType == typeof(DateTime?))
                    {
                        DateTime cleanValue = ValidateValue.ValidateDateTime(value);
                        // For DateTime, we need to handle date range searches
                        // Check if this is a "From" or "To" date field based on property name
                        if (property.Name.EndsWith("From"))
                        {
                            // For "From" dates, use GreaterThanOrEqual
                            var constantExpression = Expression.Constant(cleanValue.Date, typeof(DateTime));
                            comparison = property.PropertyType == typeof(DateTime?)
                                ? Expression.AndAlso(
                                    Expression.Property(memberExpression, "HasValue"),
                                    Expression.GreaterThanOrEqual(Expression.Property(memberExpression, "Value"), constantExpression)
                                  )
                                : Expression.GreaterThanOrEqual(memberExpression, constantExpression);
                        }
                        else if (property.Name.EndsWith("To"))
                        {
                            // For "To" dates, use LessThanOrEqual (end of day)
                            var endOfDay = cleanValue.Date.AddDays(1).AddTicks(-1);
                            var constantExpression = Expression.Constant(endOfDay, typeof(DateTime));
                            comparison = property.PropertyType == typeof(DateTime?)
                                ? Expression.AndAlso(
                                    Expression.Property(memberExpression, "HasValue"),
                                    Expression.LessThanOrEqual(Expression.Property(memberExpression, "Value"), constantExpression)
                                  )
                                : Expression.LessThanOrEqual(memberExpression, constantExpression);
                        }
                        else
                        {
                            // For regular date fields, use exact date match (same day)
                            var startOfDay = cleanValue.Date;
                            var endOfDay = cleanValue.Date.AddDays(1).AddTicks(-1);
                            var startExpression = Expression.Constant(startOfDay, typeof(DateTime));
                            var endExpression = Expression.Constant(endOfDay, typeof(DateTime));

                            if (property.PropertyType == typeof(DateTime?))
                            {
                                var hasValueCheck = Expression.Property(memberExpression, "HasValue");
                                var valueProperty = Expression.Property(memberExpression, "Value");
                                var greaterThanOrEqual = Expression.GreaterThanOrEqual(valueProperty, startExpression);
                                var lessThanOrEqual = Expression.LessThanOrEqual(valueProperty, endExpression);
                                comparison = Expression.AndAlso(hasValueCheck, Expression.AndAlso(greaterThanOrEqual, lessThanOrEqual));
                            }
                            else
                            {
                                var greaterThanOrEqual = Expression.GreaterThanOrEqual(memberExpression, startExpression);
                                var lessThanOrEqual = Expression.LessThanOrEqual(memberExpression, endExpression);
                                comparison = Expression.AndAlso(greaterThanOrEqual, lessThanOrEqual);
                            }
                        }
                    }

                    // Combine the comparison expression with the previous ones using AndAlso
                    if (comparison != null)
                    {
                        combinedExpression = combinedExpression == null
                            ? comparison
                            : Expression.AndAlso(combinedExpression, comparison);
                    }
                }
            }

            // Return the combined expression as a lambda expression
            return combinedExpression != null
                ? Expression.Lambda<Func<T, bool>>(combinedExpression, parameterExpression)
                : x => true; // Default expression if no filters are applied
        }

        private static bool IsDefault(object value)
        {
            if (value == null)
                return true;

            var type = value.GetType();
            if (type.IsValueType)
            {
                var obj = Activator.CreateInstance(type);
                if (obj != null)
                {
                    return obj.Equals(value);
                }
            }

            return false;
        }
    }
}
