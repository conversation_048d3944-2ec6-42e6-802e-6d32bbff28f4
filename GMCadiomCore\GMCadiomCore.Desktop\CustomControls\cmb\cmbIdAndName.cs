﻿namespace GMCadiomCore.Desktop.CustomControls.cmb
{
    public partial class cmbIdAndName : UserControl
    {
        private BindingList<IdAndName> lst;
        private bool _IsValueMemberId;

        public cmbIdAndName(BindingList<IdAndName> lst, bool IsValueMemberId = false)
        {
            InitializeComponent();
            this.lst = lst;
            this._IsValueMemberId = IsValueMemberId;
            Eventss();
            Fill();
            cmb.SelectedIndex = -1;
        }

        private void Eventss()
        {
            cmb.Resize += (s, e) =>
            {
                if (!this.DesignMode)
                {
                    cmb.Location = new Point(0, 0);
                    cmb.Left = 0;
                    cmb.Width = this.Width;
                }
            };
        }

        private void Fill()
        {
            if (!this.DesignMode)
            {
                if (_IsValueMemberId)
                    cmb.FillComboBox<IdAndName>(x => x.Name, x => x.Id, lst, true);
                else
                    cmb.FillComboBox<IdAndName>(x => x.Name, x => x.Name, lst, true);
                cmb.SelectedIndex = -1;
            }
        }

        public void SetValue(object value)
        {
            if (value == null)
                return;

            if (value?.GetType() == typeof(int))
                cmb.SelectedValue = ValidateValue.ValidateInt(value);
            else if (value?.GetType() == typeof(string))
                cmb.SelectedValue = ValidateValue.ValidateString(value);
            else
                cmb.SelectedValue = ValidateValue.ValidateString(value);
        }

        public object GetValue()
        {
            if (cmb.SelectedValue?.GetType() == typeof(int))
                return ValidateValue.ValidateInt(cmb.SelectedValue);
            if (cmb.SelectedValue?.GetType() == typeof(string))
                return ValidateValue.ValidateString(cmb.SelectedValue);
            else
                return ValidateValue.ValidateString(cmb.SelectedValue);
        }
    }
}
