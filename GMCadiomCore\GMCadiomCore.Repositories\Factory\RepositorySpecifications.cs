﻿namespace GMCadiomCore.Repositories.Factory
{
    public enum OrderByType
    {
        Ascending,
        Descending
    }

    public class RepositorySpecifications<T> where T : class
    {
        public Expression<Func<T, bool>>? SearchValue { get; set; }
        public Expression<Func<T, object>>? SelectValue { get; set; }
        public Func<IQueryable<T>, IIncludableQueryable<T, object>> Includes { get; set; }
        public bool IsTackable { get; set; } = false;
        public OrderByType OrderByType { get; set; } = OrderByType.Ascending;
        public Expression<Func<T, object>>? OrderBy { get; set; }
    }
}
