﻿namespace GMCadiomCore.Models.ModelValidation.Base
{
    public class TypeDescriptorFilterByAttribute
    {
        public string AttributeName { get; set; } = null!;
        public bool IsTrue { get; set; }
        public Attribute AttributeValue { get; set; } = null!;
    }

    internal class BaseCustomTypeDescriptor : CustomTypeDescriptor
    {
        public virtual IEnumerable<TypeDescriptorFilterByAttribute> FilterByAttributes { get; set; }

        public BaseCustomTypeDescriptor(ICustomTypeDescriptor? parent, object? instance) : base(parent) { }

        public override PropertyDescriptorCollection GetProperties()
        {
            IEnumerable<PropertyDescriptor> propertyDescriptors = base.GetProperties().Cast<PropertyDescriptor>();

            if (FilterByAttributes != null)
                foreach (TypeDescriptorFilterByAttribute filterByAttributes in FilterByAttributes)
                {
                    if (!filterByAttributes.IsTrue)
                        propertyDescriptors = propertyDescriptors.Where(x => x.Attributes[filterByAttributes.AttributeValue.GetType()] == null);
                }

            return new PropertyDescriptorCollection(propertyDescriptors.ToArray());
        }

        public override PropertyDescriptorCollection GetProperties(Attribute[]? attributes)
        {
            IEnumerable<PropertyDescriptor> propertyDescriptors = base.GetProperties(attributes).Cast<PropertyDescriptor>();

            if (FilterByAttributes != null)
                foreach (TypeDescriptorFilterByAttribute filterByAttributes in FilterByAttributes)
                {
                    if (!filterByAttributes.IsTrue)
                        propertyDescriptors = propertyDescriptors.Where(x => x.Attributes[filterByAttributes.AttributeValue.GetType()] == null);
                }

            return new PropertyDescriptorCollection(propertyDescriptors.ToArray());
        }
    }
}
