﻿namespace GMCadiomCore.Shared.Helper
{
    public static class Converters
    {
        public static T? FromByteArray<T>(byte[] data)
        {
            try
            {
                if (data == null)
                    return default;

                string jsonString = Encoding.UTF8.GetString(data);
                object? deserializedObject = System.Text.Json.JsonSerializer.Deserialize<T>(jsonString);

                if (typeof(T) == typeof(int))
                    deserializedObject = ValidateValue.ValidateInt(deserializedObject);
                else if (typeof(T) == typeof(string))
                    deserializedObject = ValidateValue.ValidateString(deserializedObject);

                return (T?)deserializedObject;
            }
            catch (Exception ex)
            {
                return default;
            }
        }

        public static byte[]? ToByteArray<T>(T obj)
        {
            if (obj == null)
                return null;

            string jsonString = System.Text.Json.JsonSerializer.Serialize(obj);
            byte[] serializedObject = Encoding.UTF8.GetBytes(jsonString);

            return serializedObject;
        }

        /// <summary>
        /// Get BindingList of type T from Datatable.
        /// </summary>
        /// <typeparam name="T">Custom Class.</typeparam>
        /// <param name="dataTable">Datatable that is to be converted into list of type T.</param>
        /// <returns>BindingList of T type.</returns>
        public static BindingList<T>? GetBindingListFromDataTableRevised<T>(DataTable dataTable)
        {
            BindingList<T>? lst = null;

            try
            {
                //Obtains the type of the generic class.
                Type entType = typeof(T);

                if (dataTable != null &&
                    entType != null)
                {
                    PropertyInfo? propertyInfo = null; //To hold propery information of type T.
                    object? defaultInstance = null; //To hold object of type T.
                    IFormatProvider culture = new CultureInfo("en-US", true);

                    //Create a new list of type T.
                    lst = new BindingList<T>();

                    //Each row of table is mapped with individual item of BindingList<T>.
                    foreach (DataRow row in dataTable.Rows)
                    {
                        //Create a new instance of type T to set property value.
                        defaultInstance = Activator.CreateInstance(entType);

                        if (defaultInstance != null)
                        {
                            //Each column is mapped with particular property of type T.
                            foreach (DataColumn dc in dataTable.Columns)
                            {
                                //Get property of respective datacolumn of class T.
                                propertyInfo = entType.GetProperty(dc.ColumnName);

                                //Get the value of property from row data.
                                object columnvalue = row[dc.ColumnName];

                                if (propertyInfo != null &&
                                    columnvalue != null &&
                                    columnvalue != DBNull.Value &&
                                    propertyInfo.CanWrite)
                                {
                                    Type? propertyType = Nullable.GetUnderlyingType(propertyInfo.PropertyType);

                                    #region Check if Types are same
                                    if (propertyInfo.PropertyType.FullName == columnvalue.GetType().FullName)
                                    {
                                        //Set value of particular property value in default instance of T.
                                        propertyInfo.SetValue(defaultInstance, columnvalue, null);
                                    }
                                    #endregion

                                    #region Check if Nullable then underlaying types are same
                                    else if (propertyType != null && propertyType.FullName == columnvalue.GetType().FullName)
                                    {
                                        //Set value of particular property value in default instance of T.
                                        propertyInfo.SetValue(defaultInstance, columnvalue, null);
                                    }
                                    #endregion

                                    #region Check if Date Time or Nullable DateTime Then try to parse date
                                    else if (propertyInfo.PropertyType == typeof(DateTime) ||
                                                           Nullable.GetUnderlyingType(propertyInfo.PropertyType) != null &&
                                                            Nullable.GetUnderlyingType(propertyInfo.PropertyType) == typeof(DateTime))
                                    {
                                        if (columnvalue.ToString() != "")
                                        {
                                            DateTime convertedDateTime;

                                            if (DateTime.TryParse(columnvalue.ToString(), culture, DateTimeStyles.AssumeLocal, out convertedDateTime))
                                            {
                                                propertyInfo.SetValue(defaultInstance, convertedDateTime, null);
                                            }
                                            else
                                            {
                                                //throw new Exception(string.Format("Error while Converting to DateTime. PropertyName: {0} ColumnName: {1}", propertyInfo.Name, dc.ColumnName));
                                            }
                                        }
                                    }
                                    #endregion

                                    #region If Types are not same then try to change type
                                    else
                                    {
                                        try
                                        {
                                            //For Nullable 
                                            if (propertyType != null)
                                            {
                                                var convertedValue = Convert.ChangeType(columnvalue, propertyType, culture);
                                                propertyInfo.SetValue(defaultInstance, convertedValue, null);
                                            }
                                            //For Simple
                                            else
                                            {
                                                var convertedValue = Convert.ChangeType(columnvalue, propertyInfo.PropertyType, culture);
                                                propertyInfo.SetValue(defaultInstance, convertedValue, null);
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            propertyInfo.SetValue(defaultInstance, null, null);
                                            // ex.LogAndMailException(string.Format("Error while Converting Data Type from the sheet. PropertyName: {0} ColumnName: {1}", propertyInfo.Name, dc.ColumnName));
                                            //throw;
                                            //MessageBox.Show(
                                            //    ex.Message + Environment.NewLine +
                                            //    "Error While Converting Value." + Environment.NewLine +
                                            //    "PropertyName: " + propertyInfo.Name + Environment.NewLine +
                                            //    "Value To Convert: " + columnvalue, 
                                            //    "Error!", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                        }
                                    }
                                    #endregion
                                }
                            }

                            //Now, create a class of the same type of T from default instance to add in list<T>.
                            T rtClass = (T)defaultInstance;

                            //Add the instance to BindingList<T>.
                            lst.Add(rtClass);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

            //return the final list of type T.
            return lst;
        }

        public static string FromBindingListOfStringToString(BindingList<string> strings)
        {
            StringBuilder builder = new StringBuilder();

            foreach (string item in strings)
                builder.AppendLine(item);

            return builder.ToString();
        }
    }
}
