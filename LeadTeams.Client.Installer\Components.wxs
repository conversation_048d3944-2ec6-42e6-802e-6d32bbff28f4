<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs">
  <Fragment>

    <!-- Component group for all application files -->
    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">

      <!-- Main executable -->
      <Component Id="MainExecutable" Guid="*">
        <File Id="LeadTeamsClientExe"
              Source="$(var.LeadTeams.Client.TargetPath)"
              KeyPath="yes" />
      </Component>

      <!-- Application icon -->
      <Component Id="ApplicationIcon" Guid="*">
        <File Id="ApplicationIconFile"
              Source="$(var.LeadTeams.Client.TargetDir)icons8-monitoring-96.ico"
              KeyPath="yes" />
      </Component>

      <!-- Configuration files -->
      <Component Id="ConfigFiles" Guid="*">
        <File Id="AppConfig"
              Source="$(var.LeadTeams.Client.TargetDir)App.config"
              KeyPath="yes" />
        <File Id="RuntimeConfig"
              Source="$(var.LeadTeams.Client.TargetDir)LeadTeams.Client.runtimeconfig.json" />
        <File Id="DepsJson"
              Source="$(var.LeadTeams.Client.TargetDir)LeadTeams.Client.deps.json" />
      </Component>

      <!-- Application DLL -->
      <Component Id="ApplicationDll" Guid="*">
        <File Id="LeadTeamsClientDll"
              Source="$(var.LeadTeams.Client.TargetDir)LeadTeams.Client.dll"
              KeyPath="yes" />
      </Component>

      <!-- Debug symbols (optional) -->
      <Component Id="DebugSymbols" Guid="*">
        <File Id="LeadTeamsClientPdb"
              Source="$(var.LeadTeams.Client.TargetDir)LeadTeams.Client.pdb"
              KeyPath="yes" />
      </Component>

      <!-- This will be expanded to include all necessary runtime dependencies -->
      <!-- Use Heat.exe to harvest all files from the output directory -->

    </ComponentGroup>

  </Fragment>
</Wix>
