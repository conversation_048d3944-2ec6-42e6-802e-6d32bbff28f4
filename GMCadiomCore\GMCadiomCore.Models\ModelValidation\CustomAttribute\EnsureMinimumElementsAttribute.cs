﻿namespace GMCadiomCore.Models.ModelValidation.CustomAttribute
{
    [AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
    public class EnsureMinimumElementsAttribute : ValidationAttribute
    {
        private readonly int _min;
        private readonly string _propName;

        public EnsureMinimumElementsAttribute(int min = 1, string displayName = "", [CallerMemberName] string? callerName = null)
        {
            _min = min;
            _propName = !string.IsNullOrWhiteSpace(displayName) ? displayName : callerName ?? throw new ArgumentNullException(nameof(callerName));
        }

        public override bool IsValid(object? value)
        {
            if (value is not IList list)
                return false;

            if (list.Count < _min)
            {
                ErrorMessage = $"At least {_min} item(s) must be entered in '{_propName}'.";
                return false;
            }

            var validationErrors = new List<string>();

            foreach (var item in list)
            {
                var results = new List<ValidationResult>();
                var context = new ValidationContext(item);

                if (!Validator.TryValidateObject(item, context, results, true))
                {
                    validationErrors.AddRange(results.Select(r => $"- {r.ErrorMessage}"));
                }
            }

            if (validationErrors.Any())
            {
                ErrorMessage = $"Validation failed for items in '{_propName}':\n" + string.Join("\n", validationErrors);
                return false;
            }

            return true;
        }
    }
}
