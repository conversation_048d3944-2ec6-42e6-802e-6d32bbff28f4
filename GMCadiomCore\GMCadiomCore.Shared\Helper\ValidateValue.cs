﻿namespace GMCadiomCore.Shared.Helper
{
    public static class ValidateValue
    {
        public static Ulid ValidateUlid(object? value)
        {
            Ulid result = Ulid.Empty;

            if (value is null)
                return Ulid.Empty;

            if (Ulid.TryParse(value.ToString(), out result))
            {
                return result;
            }
            else
            {
                return Ulid.Empty;
            }
        }

        public static Guid ValidateGuid(object? value)
        {
            Guid result = Guid.Empty;

            if (value is null)
                return Guid.Empty;

            if (Guid.TryParse(value.ToString(), out result))
            {
                return result;
            }
            else
            {
                return Guid.Empty;
            }
        }

        public static int ValidateInt(object? value)
        {
            int result = 0;

            if (value is null)
                return 0;

            if (int.TryParse(value.ToString(), out result))
            {
                return result;
            }
            else
            {
                return 0;
            }
        }

        public static long ValidateLong(object? value)
        {
            long result = 0;

            if (value is null)
                return 0;

            if (long.TryParse(value.ToString(), out result))
            {
                return result;
            }
            else
            {
                return 0;
            }
        }

        public static short ValidateShort(object? value)
        {
            short result = 0;

            if (value is null)
                return 0;

            if (short.TryParse(value.ToString(), out result))
            {
                return result;
            }
            else
            {
                return 0;
            }
        }

        public static decimal ValidateDecimal(object? value)
        {
            decimal result = 0;

            if (value is null)
                return 0;

            if (decimal.TryParse(value.ToString(), out result))
            {
                return Math.Round(result, 5);
            }
            else
            {
                return 0;
            }
        }

        public static double ValidateDouble(object? value)
        {
            double result = 0;

            if (value is null)
                return 0;

            if (double.TryParse(value.ToString(), out result))
            {
                return Math.Round(result, 5);
            }
            else
            {
                return 0;
            }
        }

        public static bool ValidateBool(object? value)
        {
            bool result = false;

            if (value is null)
                return false;

            if (bool.TryParse(value.ToString(), out result))
            {
                return result;
            }
            else
            {
                return false;
            }
        }

        public static DateTime ValidateDateTime(object? value)
        {
            DateTime result = DateTime.Now;

            if (value is null)
                return DateTime.Now;

            if (DateTime.TryParse(value.ToString(), out result))
            {
                return result;
            }
            else
            {
                return DateTime.Now;
            }
        }

        public static string ValidateString(object? value)
        {
            if (value is null)
                return "";

            return value.ToString() ?? string.Empty;
        }

        public static bool EmailValidation(string value)
        {
            string pattern = @"^(?!\.)(""([^""\r\\]|\\[""\r\\])*""|"
                + @"([-a-z0-9!#$%&'*+/=?^_`{|}~]|(?<!\.)\.)*)(?<!\.)"
                + @"@[a-z0-9][\w\.-]*[a-z0-9]\.[a-z][a-z\.]*[a-z]$";

            Regex regex = new Regex(pattern, RegexOptions.IgnoreCase);

            return regex.IsMatch(value);
        }

        public static bool ValuesAreEqual(object value, object filterValue)
        {
            if (value is string stringValue && filterValue is string filterString)
                return stringValue.Equals(filterString, StringComparison.CurrentCultureIgnoreCase);

            if (value is Ulid ulidValue && Ulid.TryParse(filterValue.ToString(), out var filterUlid))
                return ulidValue.Equals(filterUlid);

            if (value is Guid guidValue && Guid.TryParse(filterValue.ToString(), out var filterGuid))
                return guidValue.Equals(filterGuid);

            if (value is int intValue && int.TryParse(filterValue.ToString(), out var filterInt))
                return intValue == filterInt;

            if (value is long longValue && long.TryParse(filterValue.ToString(), out var filterLong))
                return longValue == filterLong;

            if (value is double doubleValue && double.TryParse(filterValue.ToString(), out var filterDouble))
                return doubleValue == filterDouble;

            if (value is float floatValue && float.TryParse(filterValue.ToString(), out var filterFloat))
                return floatValue == filterFloat;

            if (value is decimal decimalValue && decimal.TryParse(filterValue.ToString(), out var filterDecimal))
                return decimalValue == filterDecimal;

            if (value is DateTime dateTimeValue && DateTime.TryParse(filterValue.ToString(), out var filterDateTime))
                return dateTimeValue == filterDateTime;

            return value.Equals(filterValue);
        }

        public static string GetCleanName(string name) => name
            .Replace("Model", "")
            .Replace("View", "")
            .Replace("Repository", "")
            .Replace("Changed", "")
            .Replace("txt", "")
            .Replace("cmb", "")
            .Replace("dtp", "")
            .Replace("cbx", "")
            .Replace("lbl", "");
    }
}
