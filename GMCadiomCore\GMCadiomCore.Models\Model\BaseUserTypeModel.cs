﻿namespace GMCadiomCore.Models.Model
{
    public class UserTypeEnumeration : UlidEnumeration<UserTypeEnumeration>
    {
        public static readonly UserTypeEnumeration Admin = new UserTypeEnumeration(Ulid.Parse("01JPBH5VCQFNHEEFRPAN170618"), "Admin");
        public static readonly UserTypeEnumeration User = new UserTypeEnumeration(Ulid.Parse("01JPBH694D11N4XRP18J9HWQWX"), "User");

        private UserTypeEnumeration(Ulid key, string value) : base(key, value)
        {
        }
    }

    [Table("UserType")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<BaseUserTypeModel>))]
    public class BaseUserTypeModel : BaseIdentityModel
    {
        private string _UserTypeName;

        [Required]
        [DisplayName("User Type Name")]
        public string UserTypeName { get => _UserTypeName; set => CheckPropertyChanged(ref _UserTypeName, ref value); }
    }
}
