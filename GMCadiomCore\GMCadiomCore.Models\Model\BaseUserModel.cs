﻿namespace GMCadiomCore.Models.Model
{
    [Table("User")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<BaseUserModel>))]
    public class BaseUserModel : BaseModel
    {
        private string _UserName;
        private string _Password;
        private Ulid _UserTypeId;
        private Ulid _ScreensAccessProfileId;

        [CustomRequired]
        [DisplayName("User Name")]
        [MaxLength(50)]
        [Column(TypeName = "nvarchar(max)")]
        public string UserName { get => _UserName; set => CheckPropertyChanged(ref _UserName, ref value); }
        [CustomRequired]
        [DisplayName("Password")]
        [MaxLength(50)]
        [DataType(DataType.Password)]
        [Column(TypeName = "nvarchar(max)")]
        public string Password { get => _Password; set => CheckPropertyChanged(ref _Password, ref value); }
        [CustomRequired]
        [DisplayName("User Type")]
        public Ulid UserTypeId { get => _UserTypeId; set => CheckPropertyChanged(ref _UserTypeId, ref value); }
        [CustomRequired]
        [DisplayName("ScreensAccessProfile")]
        public Ulid ScreensAccessProfileId { get => _ScreensAccessProfileId; set => CheckPropertyChanged(ref _ScreensAccessProfileId, ref value); }
        [CustomRequired]
        [Browsable(false)]
        public virtual BaseScreensAccessProfileModel ScreensAccessProfile { get; set; }
    }
}
