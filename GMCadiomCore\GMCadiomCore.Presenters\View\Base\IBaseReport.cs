﻿namespace GMCadiomCore.Presenters.View.Base
{
    public interface IBaseReport<T> : IBaseReport
    {
        //Properties - Fields
        T ViewModel { get; }

        //Methods
        void SetBindingSource(BindingSource List);
    }

    public interface IBaseReport
    {
        //Properties - Fields
        DateTime TransactionDateFrom { get; }
        DateTime TransactionDateTo { get; }
        bool DataHasChanged { set; }

        //Events
        event EventHandler ShowEvent;
        event EventHandler SearchEvent;
        event EventHandler ExportEvent;

        //Methods
        void Show();
        DialogResult ShowDialog();
    }
}
