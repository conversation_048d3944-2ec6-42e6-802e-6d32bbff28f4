﻿namespace GMCadiomCore.Repositories.Factory
{
    public class DataConnection
    {
        public static DatabaseProviderType DatabaseProvider { get; set; }
        public static string ServerName { get; set; }
        public static string DBName { get; set; }
        public static ServerModeType ServerMode { get; set; }
        public static string UserName { get; set; }
        public static string UserPassword { get; set; }

        public enum ServerModeType
        {
            Windows,
            UserAuth
        }

        public enum DatabaseProviderType
        {
            SQL,
            MYSQL
        }

        private static string connString(string _ServerName, string _DBName, ServerModeType _ServerMode, string _UserName, string _UserPassword)
        {
            if (ServerName == "" || ServerName == null)
                ServerName = ".";
            else
                ServerName = _ServerName;

            if (DBName == "" || DBName == null)
                DBName = "GMCadiomCore";
            else
                DBName = _DBName;

            if (ServerMode.ToString() == "")
                ServerMode = ServerModeType.Windows;
            else
                ServerMode = _ServerMode;

            if (UserName == "" || UserName == null)
                UserName = "";
            else
                UserName = _UserName;

            if (UserPassword == "" || UserPassword == null)
                UserPassword = "";
            else
                UserPassword = _UserPassword;

            string[] array = new string[500];
            if (_ServerMode == ServerModeType.UserAuth)
            {
                switch (DatabaseProvider)
                {
                    case DatabaseProviderType.SQL:
                        array = new string[]
                        {
                            "Server=", ServerName,
                            " ; Initial Catalog=", DBName,
                            " ; MultipleActiveResultSets=true",
                            " ; User ID=", UserName,
                            " ; Password=", UserPassword,
                        };
                        break;
                    case DatabaseProviderType.MYSQL:
                        array = new string[]
                        {
                            "Data Source=", ServerName,
                            " ; Database=", DBName,
                            " ; Uid=", UserName,
                            " ; Pwd=", UserPassword,
                        };
                        break;
                }
            }
            else
            {
                switch (DatabaseProvider)
                {
                    case DatabaseProviderType.SQL:
                        array = new string[]
                        {
                            "Data Source=", ServerName,
                            " ; Initial Catalog=", DBName,
                            " ; Integrated Security=true",
                            " ; Connect Timeout=3600" +
                            " ; Encrypt=False" +
                            " ; TrustServerCertificate=True" +
                            " ; ApplicationIntent=ReadWrite" +
                            " ; MultiSubnetFailover=False"
                        };
                        break;
                    case DatabaseProviderType.MYSQL:
                        array = new string[]
                        {
                            "Server=", ServerName,
                            " ; Database=", DBName,
                            " ; Integrated Security=true",
                            " ; Connect Timeout=3600" +
                            " ; Encrypt=False" +
                            " ; TrustServerCertificate=True" +
                            " ; ApplicationIntent=ReadWrite" +
                            " ; MultiSubnetFailover=False"
                        };
                        break;
                }
            }
            return string.Concat(array);
        }
    }
}
