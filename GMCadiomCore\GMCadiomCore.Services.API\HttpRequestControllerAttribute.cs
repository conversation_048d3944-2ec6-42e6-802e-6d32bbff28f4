﻿namespace GMCadiomCore.Services.API
{
    [AttributeUsage(AttributeTargets.Class, AllowMultiple = false)]
    public class HttpRequestControllerAttribute : Attribute
    {
        public string ControllerName { get; }

        public HttpRequestControllerAttribute()
        {
        }

        public HttpRequestControllerAttribute(string controllerName)
        {
            ControllerName = controllerName.Replace("Controller", "").Replace("Service", "");
        }
    }
}
