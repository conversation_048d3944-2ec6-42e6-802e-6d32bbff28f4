namespace GMCadiomCore.Shared.JsonConverters
{
    public class UtcDateTimeJsonConverter : JsonConverter
    {
        private const string DateTimeFormat = "yyyy-MM-ddTHH:mm:ss.fffZ";

        public override bool CanConvert(Type objectType)
        {
            return objectType == typeof(DateTime);
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.String)
            {
                var dateTimeString = (string)reader.Value;
                if (string.IsNullOrEmpty(dateTimeString))
                    return DateTime.MinValue;

                if (DateTime.TryParse(dateTimeString, null, DateTimeStyles.RoundtripKind, out var dateTime))
                {
                    return dateTime.Kind switch
                    {
                        DateTimeKind.Utc => dateTime,
                        DateTimeKind.Local => dateTime.ToUniversalTime(),
                        DateTimeKind.Unspecified => DateTime.SpecifyKind(dateTime, DateTimeKind.Utc),
                        _ => dateTime
                    };
                }
            }
            else if (reader.TokenType == JsonToken.Date)
            {
                var dateTime = (DateTime)reader.Value;
                return dateTime.Kind switch
                {
                    DateTimeKind.Utc => dateTime,
                    DateTimeKind.Local => dateTime.ToUniversalTime(),
                    DateTimeKind.Unspecified => DateTime.SpecifyKind(dateTime, DateTimeKind.Utc),
                    _ => dateTime
                };
            }

            throw new JsonSerializationException($"Unable to convert \"{reader.Value}\" to DateTime.");
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            var dateTime = (DateTime)value;
            var utcDateTime = dateTime.Kind switch
            {
                DateTimeKind.Utc => dateTime,
                DateTimeKind.Local => dateTime.ToUniversalTime(),
                DateTimeKind.Unspecified => DateTime.SpecifyKind(dateTime, DateTimeKind.Utc),
                _ => dateTime
            };

            writer.WriteValue(utcDateTime.ToString(DateTimeFormat, CultureInfo.InvariantCulture));
        }
    }

    public class UtcNullableDateTimeJsonConverter : JsonConverter
    {
        private const string DateTimeFormat = "yyyy-MM-ddTHH:mm:ss.fffZ";

        public override bool CanConvert(Type objectType)
        {
            return objectType == typeof(DateTime?);
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null)
                return null;

            if (reader.TokenType == JsonToken.String)
            {
                var dateTimeString = (string)reader.Value;
                if (string.IsNullOrEmpty(dateTimeString))
                    return null;

                if (DateTime.TryParse(dateTimeString, null, DateTimeStyles.RoundtripKind, out var dateTime))
                {
                    return dateTime.Kind switch
                    {
                        DateTimeKind.Utc => dateTime,
                        DateTimeKind.Local => dateTime.ToUniversalTime(),
                        DateTimeKind.Unspecified => DateTime.SpecifyKind(dateTime, DateTimeKind.Utc),
                        _ => dateTime
                    };
                }
            }
            else if (reader.TokenType == JsonToken.Date)
            {
                var dateTime = (DateTime)reader.Value;
                return dateTime.Kind switch
                {
                    DateTimeKind.Utc => dateTime,
                    DateTimeKind.Local => dateTime.ToUniversalTime(),
                    DateTimeKind.Unspecified => DateTime.SpecifyKind(dateTime, DateTimeKind.Utc),
                    _ => dateTime
                };
            }

            throw new JsonSerializationException($"Unable to convert \"{reader.Value}\" to DateTime?.");
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            if (value == null)
            {
                writer.WriteNull();
                return;
            }

            var dateTime = (DateTime)value;
            var utcDateTime = dateTime.Kind switch
            {
                DateTimeKind.Utc => dateTime,
                DateTimeKind.Local => dateTime.ToUniversalTime(),
                DateTimeKind.Unspecified => DateTime.SpecifyKind(dateTime, DateTimeKind.Utc),
                _ => dateTime
            };

            writer.WriteValue(utcDateTime.ToString(DateTimeFormat, CultureInfo.InvariantCulture));
        }
    }

    public class UtcDateTimeOffsetJsonConverter : JsonConverter
    {
        private const string DateTimeFormat = "yyyy-MM-ddTHH:mm:ss.fffZ";

        public override bool CanConvert(Type objectType)
        {
            return objectType == typeof(DateTimeOffset);
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.String)
            {
                var dateTimeString = (string)reader.Value;
                if (string.IsNullOrEmpty(dateTimeString))
                    return DateTimeOffset.MinValue;

                if (DateTimeOffset.TryParse(dateTimeString, null, DateTimeStyles.RoundtripKind, out var dto))
                {
                    return dto.ToUniversalTime();
                }
            }
            else if (reader.TokenType == JsonToken.Date)
            {
                var dateTime = (DateTime)reader.Value;
                return dateTime.ToUniversalTime();
            }

            throw new JsonSerializationException($"Unable to convert \"{reader.Value}\" to DateTimeOffset.");
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            var dto = ((DateTimeOffset)value).ToUniversalTime();
            writer.WriteValue(dto.ToString(DateTimeFormat, CultureInfo.InvariantCulture));
        }
    }
}
