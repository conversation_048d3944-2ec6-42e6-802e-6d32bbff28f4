﻿namespace GMCadiomCore.Presenters.BasePresenter
{
    public class BaseListPresenter<T1, T2> : IBaseListPresenter<T1, T2> where T1 : BaseModel where T2 : BaseModel
    {
        //Fields
        public IBaseGenericListView<T2> View { get; private set; }
        public IBaseService<T1, T2> BaseService { get; private set; }
        public IBaseUnitOfWork Repository { get; private set; }
        public BindingSource MainBindingSource { get; private set; }
        public PaginationList<T2> MainList { get; protected set; }
        public T2 CurrentModel { get; private set; }
        public string ViewScreenName { get; private set; }

        //Constructor
        public BaseListPresenter(IBaseGenericListView<T2> view, IBaseUnitOfWork repository, IBaseService<T1, T2> baseService, string viewScreenName)
        {
            MainBindingSource = new BindingSource();
            View = view;
            Repository = repository;
            BaseService = baseService;
            ViewScreenName = viewScreenName;
            //Subscribe Event Handler Methods To View Events
            View.RefreshEvent += (s, e) => RefreshAction();
            View.SearchEvent += (s, e) => SearchAction();
            View.ClearSearchEvent += (s, e) => ClearSearchAction();
            View.AddNewEvent += (s, e) => AddNewAction();
            View.EditEvent += (s, e) => EditAction();
            View.DeleteEvent += (s, e) => DeleteSelectedAction();
            View.ImportEvent += (s, e) => ImportAction();
            View.ExportEvent += (s, e) => ExportAction();
            //Set Bindind Source
            View.SetBindingSource(MainBindingSource);
            //Set Pagination Event
            if (View.PaginationList != null)
            {
                View.PaginationList.RefreshData += PaginationList_Refresh;
                View.PaginationList.Previous += PaginationList_Previous;
                View.PaginationList.Next += PaginationList_Next;
                View.PaginationList.First += PaginationList_First;
                View.PaginationList.Last += PaginationList_Last;
            }
            //Load List View
            LoadAllActionList();
            //Show View
            View.Show();
        }

        //Events
        int pageNumber = 1;
        private void PaginationList_Refresh(object? sender, EventArgs e)
        {
            LoadAllActionList();
        }

        private void PaginationList_Previous(object? sender, EventArgs e)
        {
            if (MainList != null)
                if (MainList.HasPreviousPage)
                {
                    --pageNumber;
                    LoadAllActionList();
                }
        }

        private void PaginationList_Next(object? sender, EventArgs e)
        {
            if (MainList != null)
                if (MainList.HasNextPage)
                {
                    ++pageNumber;
                    LoadAllActionList();
                }
        }

        private void PaginationList_First(object? sender, EventArgs e)
        {
            pageNumber = 1;
            LoadAllActionList();
        }

        private void PaginationList_Last(object? sender, EventArgs e)
        {
            if (MainList != null)
                pageNumber = MainList.TotalPages;
            LoadAllActionList();
        }

        //Methods
        public virtual void LoadAllActionList()
        {
            LoadingHelper.ShowLoading("Loading ... Please wait ........", o =>
            {
                int? pageSize = View.PaginationList?.PageSize;
                if (pageSize != null)
                    MainList = BaseService.GetAllView(pageNumber, pageSize.Value);
                else
                    MainList = BaseService.GetAllView(pageNumber);
                MainBindingSource.DataSource = MainList;
                if (View.PaginationList != null)
                    View.PaginationList.Header = $"Page {pageNumber} / {MainList.TotalPages}";
            });
        }

        public virtual void AddNewAction()
        {
            OpenPresenter(false);
        }

        public virtual void EditAction()
        {
            CurrentModel = (T2)View.page.Current;
            OpenPresenter(true);
        }

        private void OpenPresenter(bool IsEdit)
        {
            try
            {
                var viewInstance = GetViewInstance();
                var serviceInstance = GetServiceInstance();
                if (viewInstance != null)
                {
                    var presenterInstance = GetPresenterInstance(IsEdit, viewInstance);

                    if (presenterInstance != null)
                    {
                        // Proceed with the presenter logic
                        if (presenterInstance.Instance.IsDataChanged)
                            LoadAllActionList();
                    }
                }
            }
            catch (Exception ex)
            {
                View.Message = "The screen cannot be opened, please refer to the developer";
            }
        }

        public virtual void DeleteSelectedAction()
        {
            try
            {
                CurrentModel = (T2)View.page.Current;
                if (CurrentModel != null)
                {
                    var model = BaseService.Remove(CurrentModel.Id);
                    View.Message = "Deleted successfully";
                    LoadAllActionList();
                }
                else
                {
                    if (MainBindingSource.Count > 0)
                        View.Message = "Select the row you want to delete";
                    else
                        View.Message = "There are no lines to delete";
                }
            }
            catch (DeleteException ex)
            {
                View.Message = ex.Message;
            }
            catch (Exception ex)
            {
                View.Message = "An error occurred while deleting";
            }
        }

        public virtual void ImportAction()
        {
        }

        public virtual void ExportAction()
        {
        }

        public virtual void RefreshAction()
        {
            LoadAllActionList();
        }

        public virtual void SearchAction()
        {
            LoadingHelper.ShowLoading("Loading ... Please wait ........", o =>
            {
                int? pageSize = View.PaginationList?.PageSize;
                if (pageSize != null)
                    MainList = BaseService.GetAllViewBy(View.SearchModel, pageNumber, pageSize.Value);
                else
                    MainList = BaseService.GetAllViewBy(View.SearchModel, pageNumber);
                MainBindingSource.DataSource = MainList;
            });
        }

        public virtual void ClearSearchAction()
        {
            LoadAllActionList();
        }

        private InstanceType<object>? GetViewInstance()
        {
            string formViewName = ViewScreenName;
            Type? formViewType = ReflectionExtensions.GetTypeFromAssemblyByName(formViewName);
            if (formViewType != null)
            {
                // Create view instance
                object? viewInstance = Activator.CreateInstance(formViewType);

                if (viewInstance == null)
                {
                    throw new InvalidOperationException($"Failed to create instance of {viewInstance}");
                }

                InstanceType<object> instanceType = new InstanceType<object>()
                {
                    Type = formViewType,
                    Instance = viewInstance,
                };

                return instanceType;
            }
            return null;
        }

        private InstanceType<IBaseService<T1, T2>>? GetServiceInstance()
        {
            string serviceName = ViewScreenName.Replace("View", "Service");
            Type? serviceType = ReflectionExtensions.GetTypeFromAssemblyByName(serviceName);

            if (serviceType != null)
            {
                // Create service instance
                ConstructorInfo? serviceConstructor = serviceType.GetConstructor(new Type[] { Repository.GetType() });
                IBaseService<T1, T2>? serviceInstance = serviceConstructor?.Invoke(new object[] { Repository }) as IBaseService<T1, T2>;

                if (serviceInstance == null)
                {
                    throw new InvalidOperationException($"Failed to create instance of {serviceName}");
                }

                InstanceType<IBaseService<T1, T2>> instanceType = new InstanceType<IBaseService<T1, T2>>()
                {
                    Type = serviceType,
                    Instance = serviceInstance,
                };

                return instanceType;
            }
            return null;
        }

        private InstanceType<IBasePresenter<T1, T2>>? GetPresenterInstance(bool IsEdit, InstanceType<object> viewInstance)
        {
            string presenterName = ViewScreenName.Replace("View", "Presenter");

            Type? presenterType = ReflectionExtensions.GetTypeFromAssemblyByName(presenterName);

            if (presenterType != null)
            {
                ConstructorInfo[] presenterConstructors = presenterType.GetConstructors();

                bool serviceRequired = false;

                foreach (ConstructorInfo constructorInfo in presenterConstructors)
                {
                    ParameterInfo[] parameterInfos = constructorInfo.GetParameters();
                    foreach (ParameterInfo parameterInfo in parameterInfos)
                    {
                        if (parameterInfo.Name == "service")
                        {
                            serviceRequired = true;
                        }
                    }
                }

                // Create presenter instance
                ConstructorInfo? presenterConstructor;
                IBasePresenter<T1, T2>? presenterInstance;

                InstanceType<IBaseService<T1, T2>>? serviceInstance = null!;

                if (serviceRequired)
                    serviceInstance = GetServiceInstance();

                if (serviceInstance != null)
                {
                    if (serviceRequired)
                        presenterConstructor = presenterType.GetConstructor(new Type[] { viewInstance.Type, serviceInstance.Type, Repository.GetType(), typeof(bool), typeof(T2) });
                    else
                        presenterConstructor = presenterType.GetConstructor(new Type[] { viewInstance.Type, Repository.GetType(), typeof(bool), typeof(T2) });
                    if (serviceRequired)
                        presenterInstance = presenterConstructor?.Invoke(new object[] { viewInstance.Instance, serviceInstance.Instance, Repository, true, IsEdit ? CurrentModel : null! }) as IBasePresenter<T1, T2>;
                    else
                        presenterInstance = presenterConstructor?.Invoke(new object[] { viewInstance.Instance, Repository, true, IsEdit ? CurrentModel : null! }) as IBasePresenter<T1, T2>;

                    if (presenterInstance == null)
                    {
                        throw new InvalidOperationException($"Failed to create instance of {presenterName}");
                    }

                    InstanceType<IBasePresenter<T1, T2>> instanceType = new InstanceType<IBasePresenter<T1, T2>>()
                    {
                        Type = presenterType,
                        Instance = presenterInstance,
                    };

                    return instanceType;
                }
            }
            return null;
        }

        private class InstanceType<T>
        {
            public Type Type { get; set; }
            public T Instance { get; set; }
        }
    }
}
