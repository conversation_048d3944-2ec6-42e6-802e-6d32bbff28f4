﻿namespace GMCadiomCore.Desktop.Shared.Helper
{
    public static class Utilities
    {
        public static void AutoBindControl(Control control, object dataSource, string dataMember, string? propertyName = null)
        {
            control.DataBindings.Clear();
            switch (propertyName)
            {
                case null:
                    {
                        switch (control)
                        {
                            case TextBox textBox:
                                textBox.DataBindings.Add("Text", dataSource, dataMember, true, DataSourceUpdateMode.OnPropertyChanged);
                                break;
                            case ComboBox comboBox:
                                comboBox.DataBindings.Add("SelectedValue", dataSource, dataMember, true, DataSourceUpdateMode.OnPropertyChanged);
                                break;
                            case CheckBox checkBox:
                                checkBox.DataBindings.Add("Checked", dataSource, dataMember, true, DataSourceUpdateMode.OnPropertyChanged);
                                break;
                            case RadioButton radioButton:
                                radioButton.DataBindings.Add("Checked", dataSource, dataMember, true, DataSourceUpdateMode.OnPropertyChanged);
                                break;
                            case DateTimePicker dateTimePicker:
                                dateTimePicker.DataBindings.Add("Value", dataSource, dataMember, true, DataSourceUpdateMode.OnPropertyChanged);
                                break;
                            case NumericUpDown numericUpDown:
                                numericUpDown.DataBindings.Add("Value", dataSource, dataMember, true, DataSourceUpdateMode.OnPropertyChanged);
                                break;
                            case PictureBox pictureBox:
                                pictureBox.DataBindings.Add("Image", dataSource, dataMember, true, DataSourceUpdateMode.OnPropertyChanged);
                                break;
                            case UserControl userControl:
                                {
                                    if (userControl is IBindControlIdentifier bindControlIdentifier)
                                    {
                                        Control? customControl = bindControlIdentifier as Control;
                                        if (customControl != null)
                                            customControl.DataBindings.Add(bindControlIdentifier.MainBindProperty, dataSource, dataMember, true, DataSourceUpdateMode.OnPropertyChanged);
                                        AutoBindControl(bindControlIdentifier.MainControl, dataSource, dataMember, propertyName);
                                    }
                                    else
                                        throw new NotImplementedException("Need to implemented the control type to bind on it.");
                                }
                                break;
                            case IBindControlIdentifier bindControlIdentifier:
                                {
                                    Control? customControl = bindControlIdentifier as Control;
                                    if (customControl != null)
                                        customControl.DataBindings.Add(bindControlIdentifier.MainBindProperty, dataSource, dataMember, true, DataSourceUpdateMode.OnPropertyChanged);
                                    if (customControl != bindControlIdentifier.MainControl)
                                        AutoBindControl(bindControlIdentifier.MainControl, dataSource, dataMember, propertyName);
                                }
                                break;
                        }
                        break;
                    }
                default:
                    {
                        control.DataBindings.Add(propertyName, dataSource, dataMember, true, DataSourceUpdateMode.OnPropertyChanged);
                        break;
                    }
            }

            control.Validated += (s, e) => { };
            control.Validating += (s, e) => { e.Cancel = false; };

            if (control.DataBindings.Count > 0)
                foreach (Binding binding in control.DataBindings)
                    binding.NullValue = default;
        }

        public static void RemoveArbitraryRow(TableLayoutPanel panel, int rowIndex)
        {
            if (rowIndex >= panel.RowCount)
            {
                return;
            }

            // delete all controls of row that we want to delete
            for (int i = 0; i < panel.ColumnCount; i++)
            {
                var control = panel.GetControlFromPosition(i, rowIndex);
                panel.Controls.Remove(control);
            }

            // move up row controls that comes after row we want to remove
            for (int i = rowIndex + 1; i < panel.RowCount; i++)
            {
                for (int j = 0; j < panel.ColumnCount; j++)
                {
                    var control = panel.GetControlFromPosition(j, i);
                    if (control != null)
                    {
                        panel.SetRow(control, i - 1);
                    }
                }
            }

            var removeStyle = panel.RowCount - 1;

            if (panel.RowStyles.Count > removeStyle)
                panel.RowStyles.RemoveAt(removeStyle);

            panel.RowCount--;
        }

        public static void RefreshBindings(this BindingContext context, object dataSource)
        {
            foreach (var binding in context[dataSource].Bindings.Cast<Binding>())
                binding.ReadValue();
        }

        public static void ShowKeyboard()
        {
            // => C:\Windows\system32
            var systemPath = Environment.GetFolderPath(Environment.SpecialFolder.System);
            // => C:\Windows
            var parentPath = Directory.GetParent(systemPath)?.FullName;
            if (parentPath != null)
            {
                // => C:\Windows\Sysnative\cmd.exe
                var cmdPath = Path.Combine(parentPath, "Sysnative", "cmd.exe");

                ProcessStartInfo startInfo = new ProcessStartInfo();
                startInfo.CreateNoWindow = true;
                startInfo.UseShellExecute = false;
                startInfo.FileName = cmdPath;
                startInfo.Verb = "runas";
                startInfo.Arguments = "/c osk.exe";

                try
                {
                    using (Process? process = Process.Start(startInfo))
                        process?.WaitForExit();
                }
                catch (Exception) { }
            }
        }

        public static bool IsProjectOpen()
        {
            // Check if the project is already open in windows process
            string projectName = Path.GetFileNameWithoutExtension(Application.ExecutablePath);
            Process[] processes = Process.GetProcessesByName(projectName);
            if (processes.Length > 1)
            {
                // If there are multiple instances, return true
                return true;
            }
            else
            {
                // If there is no instance or only one instance, return false
                return false;
            }
        }
    }
}
