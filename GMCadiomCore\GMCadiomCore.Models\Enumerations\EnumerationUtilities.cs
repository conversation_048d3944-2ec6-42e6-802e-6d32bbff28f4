﻿namespace GMCadiomCore.Models.Enumerations
{
    public static class EnumerationUtilities
    {
        public static List<IdAndName> ConvertListToIdAndNameList<T>(string displayMember, string valueMember, List<T> dataSource)
        {
            List<IdAndName> lst = new List<IdAndName>();
            string IdColumn = valueMember;
            string NameColumn = displayMember;

            foreach (T model in dataSource)
            {
                if (model == null) continue;

                IdAndName IdAndName = new IdAndName();

                foreach (PropertyInfo prop in model.GetType().GetProperties())
                {
                    if (prop.Name == IdColumn)
                        IdAndName.Id = ValidateValue.ValidateUlid(prop.GetValue(model, null));
                    else if (prop.Name == NameColumn)
                        IdAndName.Name = ValidateValue.ValidateString(prop.GetValue(model, null));
                }

                lst.Add(IdAndName);
            }

            return lst;
        }

        public static List<IdAndName> ConvertArrayToIdAndNameList(object[] dataSource)
        {
            List<IdAndName> lst = new List<IdAndName>();
            int i = 0;
            foreach (object model in dataSource)
            {
                if (model == null) continue;

                i++;
                IdAndName IdAndName = new IdAndName()
                {
                    Id = Ulid.Empty,
                    Name = model.ToString() ?? string.Empty,
                };

                lst.Add(IdAndName);
            }

            return lst;
        }

        public static List<IdAndName> ConvertEnumerationToIdAndNameList<TEnum>() where TEnum : Enumeration<Ulid, TEnum>
        {
            var enumerationType = typeof(TEnum);

            var fieldsForType = enumerationType
                .GetFields(
                BindingFlags.Public |
                BindingFlags.Static |
                BindingFlags.FlattenHierarchy)
                .Where(fieldInfo =>
                enumerationType.IsAssignableFrom(fieldInfo.FieldType))
                .Select(fieldInfo =>
                (TEnum)fieldInfo.GetValue(default)!);


            return fieldsForType
                       .Select(e => new IdAndName { Id = e.Value, Name = e.Name })
                       .ToList();
        }

        public static BindingList<IdAndName> ConvertListToIdAndNameList<T>(string displayMember, string valueMember, BindingList<T> dataSource)
        {
            BindingList<IdAndName> lst = new BindingList<IdAndName>();
            string IdColumn = valueMember;
            string NameColumn = displayMember;

            foreach (T model in dataSource)
            {
                if (model == null) continue;

                IdAndName IdAndName = new IdAndName();

                foreach (PropertyInfo prop in model.GetType().GetProperties())
                {
                    if (prop.Name == IdColumn)
                        IdAndName.Id = ValidateValue.ValidateUlid(prop.GetValue(model, null));
                    else if (prop.Name == NameColumn)
                        IdAndName.Name = ValidateValue.ValidateString(prop.GetValue(model, null));
                }

                lst.Add(IdAndName);
            }

            return lst;
        }
    }
}
