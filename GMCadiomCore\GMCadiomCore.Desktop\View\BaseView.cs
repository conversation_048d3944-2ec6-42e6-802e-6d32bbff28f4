﻿namespace GMCadiomCore.Desktop.View
{
    public partial class BaseView : Form
    {
        #region Fields
        private bool isRightToLift = false;
        public bool IsRightToLift
        {
            get => isRightToLift; set
            {
                isRightToLift = value;
                if (isRightToLift)
                {
                    this.RightToLeft = RightToLeft.Yes;
                    this.RightToLeftLayout = true;
                }
                else
                {
                    this.RightToLeft = RightToLeft.No;
                    this.RightToLeftLayout = false;
                }
                Invalidate();
            }
        }
        public DateTime DocumentTransactionDate = DateTime.Now.Date;
        #endregion

        public BaseView()
        {
            InitializeComponent();

            PrivateFontCollection pfc = new PrivateFontCollection();
            this.Font = new Font("Segoe UI", 8F, FontStyle.Regular);
            this.Load += (s, e) =>
            {
                foreach (Control ctrl in OpenForm.GetControls(this))
                {
                    switch (ctrl)
                    {
                        case Button button: new ButtonUI(button); break;
                        case CheckBox checkBox: new CheckBoxUI(checkBox); break;
                        case ComboBox comboBox: new ComboBoxUI(comboBox); break;
                        case DataGridView dataGridView: new DataGridViewUI(dataGridView); break;
                        case DateTimePicker dateTimePicker: new DateTimePickerUI(dateTimePicker); break;
                        case GroupBox groupBox: new GroupBoxUI(groupBox); break;
                        case Label label: new LabelUI(label); break;
                        case RadioButton radioButton: new RadioButtonUI(radioButton); break;
                        case StatusStrip statusStrip: new StatusStripUI(statusStrip); break;
                        case TabControl tabControl: new TabControlUI(tabControl); break;
                        case TableLayoutPanel tableLayoutPanel: new TableLayoutPanelUI(tableLayoutPanel); break;
                        case TabPage tabPage: new TabPageUI(tabPage); break;
                        case TextBox textBox: new TextBoxUI(textBox); break;
                        case ToolStrip toolStrip: new ToolStripUI(toolStrip); break;
                    }
                }
            };
        }
    }
}
