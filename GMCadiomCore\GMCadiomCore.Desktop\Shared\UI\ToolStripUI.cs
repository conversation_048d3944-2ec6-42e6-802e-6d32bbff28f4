﻿namespace GMCadiomCore.Desktop.Shared.UI
{
    public class ToolStripUI
    {
        public ToolStripUI(ToolStrip obj)
        {
            obj.BackColor = ColorsSchema.ToolStrip.BackColor;
            obj.ForeColor = ColorsSchema.ToolStrip.ForeColor;
            obj.RightToLeft = ColorsSchema.IsRTL;
            //obj.MouseEnter += (s, e) =>
            //{
            //    // Cast to allow reuse of method.
            //    ToolStripItem tsi = s as ToolStripItem;
            //    if (tsi != null)
            //    {
            //        //Create semi-transparent picture.
            //        Bitmap bm = new Bitmap(tsi.Width, tsi.Height);
            //        for (int y = 0; y < tsi.Height; y++)
            //        {
            //            for (int x = 0; x < tsi.Width; x++)
            //                bm.SetPixel(x, y, Color.FromArgb(232, 72, 85)); // TODO :
            //        }

            //        // Set background.
            //        tsi.BackgroundImage = bm;
            //    }
            //};
            //obj.MouseLeave += (s, e) =>
            //{
            //    ToolStripItem tsi = s as ToolStripItem;
            //    if (tsi != null)
            //        tsi.BackgroundImage = null;
            //};

            //foreach (Control ctrl in OpenForm.GetControls(obj))
            //{
            //    if (ctrl is ToolStripButton)
            //    {
            //    }
            //}
        }
    }
}
