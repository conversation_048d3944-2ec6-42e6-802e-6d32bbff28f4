﻿namespace GMCadiomCore.Presenters.BasePresenter
{
    public class BasePresenter<T1, T2> : IBasePresenter<T1, T2> where T1 : BaseModel where T2 : BaseModel
    {
        //Fields
        public bool IsDataChanged { get; set; }
        public IBaseView<T1> View { get; private set; }
        public IBaseUnitOfWork Repository { get; private set; }
        public IBaseService<T1, T2> BaseService { get; private set; }
        public T1 CurrentModel { get => View.CurrentModel; set => SetCurrentModelFromJSON(value); }
        public BindingList<T1>? TempList { get; set; }
        public string TempListPath => Path.Combine(SessionPaths.SessionsTempListPath(), typeof(T1).Name);

        //Constructor
        public BasePresenter(IBaseView<T1> view, IBaseUnitOfWork repository, IBaseService<T1, T2> baseService)
        {
            View = view;
            Repository = repository;
            BaseService = baseService;
            //Subscribe event handler methods to view events
            View.AddNewEvent += (s, e) => AddNewAction();
            View.DeleteEvent += (s, e) => DeleteSelectedAction();
            View.SaveEvent += (s, e) => SaveAction();
            View.SaveTempEvent += (s, e) => SaveTempAction();
            View.GetTempEvent += (s, e) => GetTempAction();
            //Fill Temp List From JSON
            GetTempListFromJSON();
            IsDataChanged = false;
        }

        //Methods
        public virtual void LoadAllLists()
        {

        }

        public virtual void AddNewAction()
        {
            //Clean View
            CleanViewFieldsAction();
        }

        public virtual void DeleteSelectedAction()
        {
            try
            {
                if (View.Id != Ulid.Empty)
                {
                    BaseService.Remove(View.Id);
                    View.IsSuccessful = true;
                    View.Message = "Deleted successfully";
                }
            }
            catch (DeleteException ex)
            {
                View.IsSuccessful = false;
                View.Message = ex.Message;
            }
            catch (Exception ex)
            {
                View.IsSuccessful = false;
                View.Message = "An error occurred while deleting";
            }
        }

        public virtual void EditModelBeforeSave()
        {
            BaseService.EditModelBeforeSave(CurrentModel);
        }

        public virtual void SaveAction()
        {
            try
            {
                View.IsEdit = BaseService.IsExist(View.Id);

                EditModelBeforeSave();

                if (View.IsEdit)
                {
                    BaseService.Update(CurrentModel);
                    View.Message = "Modified successfully";
                }
                else
                {
                    BaseService.Add(CurrentModel);
                    View.Message = "Saved successfully";
                }
                View.IsSuccessful = true;
                CleanViewFieldsAction();
            }
            catch (Exception ex)
            {
                View.IsSuccessful = false;
                View.Message = ex.Message;
            }
        }

        public virtual void GetCurrentModelToJSON()
        {

        }

        public virtual void SetCurrentModelFromJSON(T1 Model)
        {
            View.CurrentModel = Model;
            View.CurrentModel.PropertyChanged += CurrentModel_PropertyChanged;
        }

        public virtual void SaveTempAction()
        {
            GetCurrentModelToJSON();
            View.TempList.Add(CurrentModel);
            SaveTempListToJSON();
            CleanViewFieldsAction();
        }

        private void GetTempAction()
        {
            if (View.TempList.Current is T1 value)
            {
                SetCurrentModelFromJSON(value);
                View.TempList.RemoveCurrent();
                SaveTempListToJSON();
            }
        }

        public virtual void CleanViewFieldsAction()
        {
            View.IsEdit = false;
            CurrentModel = Activator.CreateInstance<T1>();
            View.Id = CurrentModel.Id;
        }

        public virtual void CurrentModel_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == null)
                return;

            IsDataChanged = true;
        }

        private void GetTempListFromJSON()
        {
            try
            {
                if (System.IO.File.Exists(TempListPath))
                {
                    string jsonContent = System.IO.File.ReadAllText(TempListPath);
                    TempList = JsonConvert.DeserializeObject<BindingList<T1>>(jsonContent, new JsonSerializerSettings
                    {
                        PreserveReferencesHandling = PreserveReferencesHandling.Objects
                    });
                    View.TempList = new BindingSource();
                    View.TempList.DataSource = TempList;
                }
                if (View.TempList == null)
                    TempList = new BindingList<T1>();
            }
            catch (Exception ex)
            {
                if (View.TempList == null)
                    TempList = new BindingList<T1>();
            }
        }

        private void SaveTempListToJSON()
        {
            try
            {
                string jsonContent = JsonConvert.SerializeObject(View.TempList, Formatting.Indented, new JsonSerializerSettings
                {
                    PreserveReferencesHandling = PreserveReferencesHandling.Objects
                });
                System.IO.File.WriteAllText(TempListPath, jsonContent);
            }
            catch (Exception ex)
            {

            }
        }
    }
}
