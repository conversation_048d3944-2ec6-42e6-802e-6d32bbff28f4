﻿namespace GMCadiomCore.Desktop.Extensions
{
    public static class ControlExtensions
    {
        public static void SafelyInvokeAction(this Control control, Action action)
        {
            if (control == null)
                return;
            if (action == null)
                return;
            if (control.IsDisposed)
                return;
            try
            {
                if (control.InvokeRequired)
                    control.Invoke(action);
                else
                    action();
            }
            catch
            {

            }
        }
    }
}
