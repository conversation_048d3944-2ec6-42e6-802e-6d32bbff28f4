﻿namespace GMCadiomCore.Models.ModelValidation.CustomAttribute
{
    public class SearchableAttribute : Attribute
    {
        public readonly SelectControls SelectedControls;
        public readonly string ModelName;

        public enum SelectControls
        {
            Text<PERSON>ox,
            <PERSON>mboBox,
            CheckBox,
            DateTimePicker,
        }

        public SearchableAttribute(SelectControls selectControls, string modelName)
        {
            SelectedControls = selectControls;
            ModelName = modelName;
        }
    }
}
