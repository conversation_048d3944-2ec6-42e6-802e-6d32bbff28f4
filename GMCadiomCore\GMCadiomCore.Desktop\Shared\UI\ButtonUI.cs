﻿namespace GMCadiomCore.Desktop.Shared.UI
{
    public class ButtonUI
    {
        public ButtonUI(Button obj)
        {
            obj.BackColor = ColorsSchema.Button.BackColor;
            obj.ForeColor = ColorsSchema.Button.ForeColor;
            obj.RightToLeft = ColorsSchema.IsRTL;
            obj.FlatStyle = FlatStyle.Popup;
            //obj.FlatAppearance.BorderColor = Color.FromArgb(232, 72, 85);
            //obj.FlatAppearance.BorderSize = 2;
        }
    }
}
