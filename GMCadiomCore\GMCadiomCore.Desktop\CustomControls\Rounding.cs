﻿namespace GMCadiomCore.Desktop.CustomControls
{
    public sealed class Rounding
    {
        public enum RoundingStyle : byte
        {
            All,
            Top,
            Bottom,
            Left,
            Right,
            TopRight,
            BottomRight
        }

        public static void DrawStringWithAlignment(Graphics G, string T, Font F, Color C, Rectangle R, StringFormat SF, ContentAlignment textAlign)
        {
            SizeF sizeF = G.MeasureString(T, F);
            PointF point = new PointF();

            switch (textAlign)
            {
                case ContentAlignment.TopLeft:
                    point = new PointF(R.Left, R.Top);
                    break;
                case ContentAlignment.TopCenter:
                    point = new PointF(R.Left + (R.Width - sizeF.Width) / 2, R.Top);
                    break;
                case ContentAlignment.TopRight:
                    point = new PointF(R.Right - sizeF.Width, R.Top);
                    break;
                case ContentAlignment.MiddleLeft:
                    point = new PointF(R.Left, R.Top + (R.Height - sizeF.Height) / 2);
                    break;
                case ContentAlignment.MiddleCenter:
                    point = new PointF(R.Left + (R.Width - sizeF.Width) / 2, R.Top + (R.Height - sizeF.Height) / 2);
                    break;
                case ContentAlignment.MiddleRight:
                    point = new PointF(R.Right - sizeF.Width, R.Top + (R.Height - sizeF.Height) / 2);
                    break;
                case ContentAlignment.BottomLeft:
                    point = new PointF(R.Left, R.Bottom - sizeF.Height);
                    break;
                case ContentAlignment.BottomCenter:
                    point = new PointF(R.Left + (R.Width - sizeF.Width) / 2, R.Bottom - sizeF.Height);
                    break;
                case ContentAlignment.BottomRight:
                    point = new PointF(R.Right - sizeF.Width, R.Bottom - sizeF.Height);
                    break;
            }

            using (SolidBrush solidBrush = new SolidBrush(C))
            {
                G.DrawString(T, F, solidBrush, point, SF);
            }
        }

        public static void CenterString(Graphics G, string T, Font F, Color C, Rectangle R)
        {
            SizeF sizeF = G.MeasureString(T, F);
            using (SolidBrush solidBrush = new SolidBrush(C))
            {
                G.DrawString(T, F, solidBrush, checked(new Point((int)Math.Round(unchecked(R.Width / 2.0 - (double)(sizeF.Width / 2f))), (int)Math.Round(unchecked(R.Height / 2.0 - (double)(sizeF.Height / 2f))))));
            }
        }

        public static Color ColorFromHex(string Hex)
        {
            return Color.FromArgb(checked((int)long.Parse(string.Format("FFFFFFFFFF{0}", Hex.Substring(1)), NumberStyles.HexNumber)));
        }

        public static Rectangle FullRectangle(Size S, bool Subtract)
        {
            Rectangle result;
            if (Subtract)
            {
                result = checked(new Rectangle(0, 0, S.Width - 1, S.Height - 1));
            }
            else
            {
                result = new Rectangle(0, 0, S.Width, S.Height);
            }

            return result;
        }

        public static GraphicsPath RoundRect(Rectangle rect, int rounding, RoundingStyle style = RoundingStyle.All)
        {
            GraphicsPath path = new GraphicsPath();

            if (rounding == 0)
            {
                path.AddRectangle(rect);
                path.CloseFigure();
                return path;
            }

            int d = rounding * 2; // Diameter of the rounding arcs
            int x = rect.X, y = rect.Y, w = rect.Width, h = rect.Height;

            switch (style)
            {
                case RoundingStyle.All:
                    path.AddArc(x, y, d, d, 180, 90);
                    path.AddArc(x + w - d, y, d, d, 270, 90);
                    path.AddArc(x + w - d, y + h - d, d, d, 0, 90);
                    path.AddArc(x, y + h - d, d, d, 90, 90);
                    break;

                case RoundingStyle.Top:
                    path.AddArc(x, y, d, d, 180, 90);
                    path.AddArc(x + w - d, y, d, d, 270, 90);
                    path.AddLine(x + w, y + h, x, y + h);
                    break;

                case RoundingStyle.Bottom:
                    path.AddLine(x, y, x + w, y);
                    path.AddArc(x + w - d, y + h - d, d, d, 0, 90);
                    path.AddArc(x, y + h - d, d, d, 90, 90);
                    break;

                case RoundingStyle.Left:
                    path.AddArc(x, y, d, d, 180, 90);
                    path.AddLine(x + w, y, x + w, y + h);
                    path.AddArc(x, y + h - d, d, d, 90, 90);
                    break;

                case RoundingStyle.Right:
                    path.AddLine(x, y + h, x, y);
                    path.AddArc(x + w - d, y, d, d, 270, 90);
                    path.AddArc(x + w - d, y + h - d, d, d, 0, 90);
                    break;

                case RoundingStyle.TopRight:
                    path.AddLine(x, y + 1, x, y);
                    path.AddArc(x + w - d, y, d, d, 270, 90);
                    path.AddLine(x + w, y + h, x + w, y + h);
                    path.AddLine(x + 1, y + h, x, y + h);
                    break;

                case RoundingStyle.BottomRight:
                    path.AddLine(x, y + 1, x, y);
                    path.AddLine(x + w - 1, y, x + w, y);
                    path.AddArc(x + w - d, y + h - d, d, d, 0, 90);
                    path.AddLine(x + 1, y + h, x, y + h);
                    break;
            }

            path.CloseFigure();
            return path;
        }

        /// <summary>
        /// Creates color with corrected brightness.
        /// </summary>
        /// <param name="color">Color to correct.</param>
        /// <param name="correctionFactor">The brightness correction factor. Must be between -1 and 1. 
        /// Negative values produce darker colors.</param>
        /// <returns>
        /// Corrected <see cref="Color"/> structure.
        /// </returns>
        public static Color ChangeColorBrightness(Color color, float correctionFactor)
        {
            float red = color.R;
            float green = color.G;
            float blue = color.B;

            if (correctionFactor < 0)
            {
                correctionFactor = 1 + correctionFactor;
                red *= correctionFactor;
                green *= correctionFactor;
                blue *= correctionFactor;
            }
            else
            {
                red = (255 - red) * correctionFactor + red;
                green = (255 - green) * correctionFactor + green;
                blue = (255 - blue) * correctionFactor + blue;
            }

            return Color.FromArgb(color.A, (int)red, (int)green, (int)blue);
        }

        public static GraphicsPath CreateRoundRect(float X, float Y, float Width, float Height, float Radius)
        {
            GraphicsPath GP = new();
            GP.AddLine(X + Radius, Y, X + Width - Radius * 2, Y);
            GP.AddArc(X + Width - Radius * 2, Y, Radius * 2, Radius * 2, 270, 90);

            GP.AddLine(X + Width, Y + Radius, X + Width, Y + Height - Radius * 2);
            GP.AddArc(X + Width - Radius * 2, Y + Height - Radius * 2, Radius * 2, Radius * 2, 0, 90);

            GP.AddLine(X + Width - Radius * 2, Y + Height, X + Radius, Y + Height);
            GP.AddArc(X, Y + Height - Radius * 2, Radius * 2, Radius * 2, 90, 90);

            GP.AddLine(X, Y + Height - Radius * 2, X, Y + Radius);
            GP.AddArc(X, Y, Radius * 2, Radius * 2, 180, 90);

            GP.CloseFigure();

            return GP;
        }
    }
}
