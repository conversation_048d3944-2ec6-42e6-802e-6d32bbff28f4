﻿namespace GMCadiomCore.Desktop.View
{
    public static class SearchUtilities
    {
        public static object? GetControlValue(Control control)
        {
            if (control is TextBox textBox)
                return (textBox).Text;
            if (control is ComboBox comboBox)
                return (comboBox).SelectedValue;
            if (control is Check<PERSON><PERSON> checkBox)
                return (checkBox).Checked;
            if (control is DateTimePicker dateTimePicker)
                return (dateTimePicker).Value;

            return null;
        }

        public static void SetControlValue(Control control, object value)
        {
            if (value != null)
            {
                if (control is TextBox textBox)
                    textBox.Text = ValidateValue.ValidateString(value);
                if (control is ComboBox comboBox)
                    comboBox.SelectedValue = value;
                if (control is CheckBox checkBox)
                    checkBox.Checked = (bool)value;
                if (control is DateTimePicker dateTimePicker)
                    dateTimePicker.Value = ValidateValue.ValidateDateTime(value);
            }
        }

        public static object? GetSearchValue(List<ControlsValueModel> listControlValue, [CallerMemberName] string name = "")
        {
            var value = listControlValue.FirstOrDefault(x => x.labelName == name);
            if (value != null)
                return value.valueControl;
            else
                return null;
        }

        public static string GetListControlsValues(List<ControlsValueModel> listControlValue)
        {
            string stringValue = "";

            listControlValue.ForEach(x =>
            {
                if (!string.IsNullOrEmpty(x.valueControl?.ToString()))
                {
                    var value = x.valueControl.GetType() == typeof(DateTime) ? ValidateValue.ValidateDateTime(x.valueControl).Date.ToShortDateString() : x.valueControl;

                    stringValue += $"{x.labelText} : {value}";

                    if (listControlValue.IndexOf(x) != listControlValue.Count - 1)
                        stringValue += $" | ";
                }
            });

            return stringValue;
        }
    }
}
