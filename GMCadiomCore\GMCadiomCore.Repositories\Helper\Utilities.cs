﻿namespace GMCadiomCore.Repositories.Helper
{
    public static class Utilities
    {
        public static string SetParameterSchema(PropertyInfo propertyInfo)
        {
            TypeCode typeCode = Type.GetTypeCode(propertyInfo.PropertyType);
            switch (typeCode)
            {
                case TypeCode.Int32: return $"CONVERT(INT, @{propertyInfo.Name})";
                case TypeCode.String: return $"@{propertyInfo.Name}";
                case TypeCode.Byte: return $"CONVERT(VARBINARY(MAX), @{propertyInfo.Name})";
                case TypeCode.Boolean: return $"CONVERT(BIT, @{propertyInfo.Name})";
                case TypeCode.DateTime: return $"CONVERT(DATETIME, @{propertyInfo.Name}, 103)";
                default: return $"@{propertyInfo.Name}";
            }
        }

        public static string GetEquality(ExpressionType expressionType)
        {
            switch (expressionType)
            {
                case ExpressionType.Equal: return "=";
                case ExpressionType.AndAlso: return "AND";
                case ExpressionType.OrElse: return "OR";
                case ExpressionType.NotEqual: return "<>";
                case ExpressionType.GreaterThan: return ">";
                case ExpressionType.GreaterThanOrEqual: return ">=";
                case ExpressionType.LessThan: return "<";
                case ExpressionType.LessThanOrEqual: return "<=";
                default: throw new Exception($"Can't Find Equality ExpressionType For : {expressionType}");
            }
        }

        public static object SetCleanValue(object value)
        {
            TypeCode typeCode = Type.GetTypeCode(value.GetType());
            switch (typeCode)
            {
                case TypeCode.Int32: return $"'{ValidateValue.ValidateInt(value)}'";
                case TypeCode.String: return $"'{ValidateValue.ValidateString(value)}'";
                case TypeCode.Boolean: return $"'{value}'";
                case TypeCode.DateTime: return $"CONVERT(datetime, '{ValidateValue.ValidateDateTime(value).ToString("dd/MM/yyyy HH:mm:ss")}', 103)";
                case TypeCode.Decimal: return $"'{ValidateValue.ValidateDecimal(value)}'";
                default: throw new NotImplementedException($"No implementation for object {value}");
            }
        }

        public static string FixODBCReservedKeywords(string value)
        {
            return value.Replace("dbo.User", "dbo.[User]");
        }

        public static Dictionary<string, object?> ModelToDictionary<T>(object model)
        {
            PropertyInfo[] propertyInfos = typeof(T).GetProperties().Where(prop => !prop.IsDefined(typeof(NotMappedAttribute), false)).ToArray();

            Dictionary<string, object?> keyValuePairs = new Dictionary<string, object?>();

            foreach (PropertyInfo propertyInfo in propertyInfos)
            {
                var propertyValue = propertyInfo.GetValue(model, null);
                keyValuePairs.Add(propertyInfo.Name, propertyValue);
            }

            return keyValuePairs;
        }

        public static string GetCorrectPropertyName<T>(Expression<Func<T, object>> expression)
        {
            if (expression.Body is MemberExpression memberExpression)
            {
                return memberExpression.Member.Name;
            }
            else if (expression.Body is ConditionalExpression conditionalExpression)
            {
                object? delegation = Expression.Lambda(conditionalExpression.Test).Compile().DynamicInvoke();
                if (delegation != null && delegation is bool boolValue)
                    return ((MemberExpression)(boolValue ? conditionalExpression.IfTrue : conditionalExpression.IfFalse)).Member.Name;
                return string.Empty;
            }
            else
            {
                var op = ((UnaryExpression)expression.Body).Operand;
                return ((MemberExpression)op).Member.Name;
            }
        }

        public static Expression GetLeftNode(Expression expression)
        {
            dynamic exp = expression;
            return (Expression)exp.Left;
        }

        public static Expression GetRightNode(Expression expression)
        {
            dynamic exp = expression;
            return (Expression)exp.Right;
        }

        public static Expression<Func<TSource, bool>>? GetGlobalFilter<TSource>(List<BaseQueryBuilder.GlobalFilter> globalFilters)
        {
            if (globalFilters == null)
                return null;

            Type entityType = typeof(TSource);

            foreach (BaseQueryBuilder.GlobalFilter globalFilter in globalFilters)
                if (globalFilter.IgnoreTables.Contains(entityType.GetTableNameValue()))
                    return null;

            Expression? combinedFilter = null;
            var parameter = Expression.Parameter(entityType, "e");

            foreach (BaseQueryBuilder.GlobalFilter globalFilter in globalFilters)
            {
                PropertyInfo? propertyInfo = entityType.GetProperty(globalFilter.ColumnName);
                if (propertyInfo == null)
                    continue;

                object keyValue = globalFilter.Value;

                if (keyValue != null)
                {
                    // Access the foreign key property in the entity
                    var property = Expression.PropertyOrField(parameter, propertyInfo.Name);
                    var propertyType = property.Type;

                    // If the key value is a list (e.g., multiple IDs)
                    if (keyValue is IEnumerable keyValueEnumerable)
                    {
                        var keyValueList = keyValueEnumerable.Cast<object>().ToList();

                        if (keyValueList.Count > 0)
                        {
                            var containsMethod = typeof(IList).GetMethod("Contains", new[] { typeof(object) });

                            if (containsMethod != null)
                            {
                                Expression listFilter;
                                MethodCallExpression containsCall;

                                // Handle nullable properties
                                if (propertyType.IsGenericType && propertyType.GetGenericTypeDefinition() == typeof(Nullable<>))
                                {
                                    var hasValueProperty = Expression.Property(property, "HasValue");
                                    var valueProperty = Expression.Property(property, "Value");

                                    containsCall = Expression.Call(Expression.Constant(keyValueList), containsMethod, Expression.Convert(valueProperty, typeof(object)));
                                    listFilter = Expression.AndAlso(hasValueProperty, containsCall);
                                }
                                else
                                {
                                    containsCall = Expression.Call(Expression.Constant(keyValueList), containsMethod, Expression.Convert(property, typeof(object)));
                                    listFilter = containsCall;
                                }

                                combinedFilter = combinedFilter == null
                                    ? listFilter
                                    : Expression.AndAlso(combinedFilter, listFilter);
                            }
                        }
                    }
                    else
                    {
                        Expression equalExpression;
                        ConstantExpression constant;

                        if (propertyType.IsGenericType && propertyType.GetGenericTypeDefinition() == typeof(Nullable<>))
                        {
                            var hasValueProperty = Expression.Property(property, "HasValue");
                            var valueProperty = Expression.Property(property, "Value");

                            constant = Expression.Constant(keyValue, propertyType);
                            equalExpression = Expression.AndAlso(hasValueProperty, Expression.Equal(valueProperty, constant));
                        }
                        else
                        {
                            constant = Expression.Constant(keyValue);
                            equalExpression = Expression.Equal(property, constant);
                        }

                        combinedFilter = combinedFilter == null
                            ? equalExpression
                            : Expression.AndAlso(combinedFilter, equalExpression);
                    }
                }
            }

            // If no filters were combined, return null
            if (combinedFilter is null)
                return null;

            // Build and return the final lambda expression
            Expression<Func<TSource, bool>> expression = Expression.Lambda<Func<TSource, bool>>(combinedFilter, parameter);

            return expression;
        }
    }
}
