﻿namespace GMCadiomCore.Repositories.IRepository
{
    public interface IBaseRepository<TEntity, TEntityView> where TEntity : class where TEntityView : class
    {
        #region Async
        Task<TEntity?> AddAsync(TEntity? entity);
        Task<TEntity?> UpdateAsync(TEntity? entity);
        Task<TEntity?> RemoveAsync(TEntity? entity);
        Task<TEntity?> GetAsync(RepositorySpecifications<TEntity>? repositorySpecifications = null);
        Task<bool> IsExistAsync(Ulid id);
        Task<PaginationList<TEntity>> GetAllPaginationListAsync(PaginationSpecifications<TEntity> paginationSpecifications);
        Task<List<TEntity>> GetAllAsync(RepositorySpecifications<TEntity>? repositorySpecifications = null);
        Task<PaginationList<TEntity>> GetAllByNameAsync(string name, PaginationSpecifications<TEntity> paginationSpecifications);
        Task<List<IdAndName>> GetAsSelectedItemsAsync(RepositorySpecifications<TEntity>? repositorySpecifications = null);
        Task<PaginationList<TEntityView>> GetAllViewAsync(PaginationSpecifications<TEntityView> paginationSpecifications);
        Task<int> GetMaxIdAsync(RepositorySpecifications<TEntity>? repositorySpecifications = null);
        #endregion

        #region Sync
        TEntity? Add(TEntity? entity);
        TEntity? Update(TEntity? entity);
        TEntity? Remove(TEntity? entity);
        TEntity? Get(RepositorySpecifications<TEntity>? repositorySpecifications = null);
        bool IsExist(Ulid id);
        PaginationList<TEntity> GetAllPaginationList(PaginationSpecifications<TEntity> paginationSpecifications);
        List<TEntity> GetAll(RepositorySpecifications<TEntity>? repositorySpecifications = null);
        PaginationList<TEntity> GetAllByName(string name, PaginationSpecifications<TEntity> paginationSpecifications);
        List<IdAndName> GetAsSelectedItems(RepositorySpecifications<TEntity>? repositorySpecifications = null);
        PaginationList<TEntityView> GetAllView(PaginationSpecifications<TEntityView> paginationSpecifications);
        int GetMaxId(RepositorySpecifications<TEntity>? repositorySpecifications = null);
        #endregion
    }
}
