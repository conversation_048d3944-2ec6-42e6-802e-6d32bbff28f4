﻿namespace GMCadiomCore.Authentications.PermissionsAndSessions
{
    public static class MachineData
    {
        private static string GetCPUInfo(string Win32_Name, string Name)
        {
            string info = string.Empty;
            ManagementClass mc = new ManagementClass(Win32_Name);
            ManagementObjectCollection moc = mc.GetInstances();

            foreach (ManagementObject mo in moc)
            {
                info = mo.Properties[Name].Value?.ToString() ?? string.Empty;
                break;
            }
            return info;
        }

        private static string ToHEX(string decString)
        {
            byte[] bytes = Encoding.Default.GetBytes(decString);
            string hexString = BitConverter.ToString(bytes).Replace("-", "");
            return hexString;
        }

        private static string GetHardDiskSerial()
        {
            ManagementObjectSearcher moSearcher = new ManagementObjectSearcher("SELECT * FROM Win32_DiskDrive");
            var hddd = new BindingList<HardDrive>();
            foreach (ManagementObject wmi_HD in moSearcher.Get())
            {
                HardDrive hd = new HardDrive();  // User Defined Class
                hd.Model = wmi_HD["Model"].ToString() ?? string.Empty;  //Model Number
                hd.Type = wmi_HD["InterfaceType"].ToString() ?? string.Empty;  //Interface Type
                hd.SerialNo = wmi_HD["SerialNumber"].ToString() ?? string.Empty; //Serial Number
                hddd.Add(hd);
            }
            return hddd.Where(x => x.Type == "IDE").FirstOrDefault()?.SerialNo.ToString() ?? string.Empty;
        }

        public static string GetSerial()
        {
            string ProcessorId = ToHEX(GetCPUInfo("Win32_Processor", "ProcessorId"));
            string Model = ToHEX(GetCPUInfo("Win32_ComputerSystem", "Model"));
            string TotalPhysicalMemory = ToHEX(GetCPUInfo("Win32_ComputerSystem", "TotalPhysicalMemory"));
            string HardDisk = ToHEX(GetHardDiskSerial());

            return string.Concat(ProcessorId, Model, TotalPhysicalMemory, HardDisk);
        }

        public static string GetPOSSerial()
        {
            string SerialNumber = GetCPUInfo("Win32_BaseBoard", "SerialNumber");
            return SerialNumber;
        }

        public class HardDrive
        {
            public string Model { get; set; }
            public string Type { get; set; }
            public string SerialNo { get; set; }
        }
    }
}
