﻿namespace GMCadiomCore.Services.Server
{
    public class BaseService<TEntity, TEntityView> : IBaseService<TEntity, TEntityView> where TEntity : BaseIdentityModel where TEntityView : BaseIdentityModel
    {
        protected readonly IBaseRepository<TEntity, TEntityView> _repository;

        protected virtual Expression<Func<TEntity, bool>>? SearchValue => null;
        protected virtual Func<IQueryable<TEntity>, IIncludableQueryable<TEntity, object>>? Includes => null;

        public BaseService(IBaseRepository<TEntity, TEntityView> repository)
        {
            _repository = repository;
        }

        public virtual void EditModelBeforeSave(TEntity model)
        {
        }

        public virtual void ValidateEntity(TEntity model, bool isEdit)
        {
            new BaseValidation().Validate(model);
        }

        public virtual TEntity SetEntity(TEntity model, TEntity entity)
        {
            if (model == null || entity == null)
                throw new ArgumentNullException("Model and entity cannot be null");

            var properties = typeof(TEntity).GetProperties();

            foreach (var property in properties)
            {
                if (property.CanRead && property.CanWrite)
                {
                    var value = property.GetValue(model);
                    property.SetValue(entity, value);
                }
            }

            return entity;
        }

        #region Async
        public virtual async Task<ServiceResult<IEnumerable<TEntity>>> GetAllAsync()
        {
            try
            {
                RepositorySpecifications<TEntity> repositorySpecifications = new RepositorySpecifications<TEntity>()
                {
                    SearchValue = SearchValue,
                    Includes = Includes,
                };
                var result = await _repository.GetAllAsync(repositorySpecifications);

                if (result == null)
                    return ServiceResult<IEnumerable<TEntity>>.Failure("No data found");
                else
                    return ServiceResult<IEnumerable<TEntity>>.Success(result);
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<TEntity>>.Failure(ex.Message);
            }
        }

        public virtual async Task<ServiceResult<PaginationList<TEntity>>> GetAllPaginationListAsync(int pageNumber, int pageSize = 25)
        {
            try
            {
                PaginationSpecifications<TEntity> paginationSpecifications = new PaginationSpecifications<TEntity>()
                {
                    SearchValue = SearchValue,
                    Includes = Includes,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                };
                var result = await _repository.GetAllPaginationListAsync(paginationSpecifications);

                if (result == null)
                    return ServiceResult<PaginationList<TEntity>>.Failure("No data found");
                else
                    return ServiceResult<PaginationList<TEntity>>.Success(result);
            }
            catch (Exception ex)
            {
                return ServiceResult<PaginationList<TEntity>>.Failure(ex.Message);
            }
        }

        public virtual async Task<ServiceResult<PaginationList<TEntity>>> GetAllByNameAsync(string name, int pageNumber, int pageSize = 25)
        {
            try
            {
                PaginationSpecifications<TEntity> paginationSpecifications = new PaginationSpecifications<TEntity>()
                {
                    SearchValue = SearchValue,
                    Includes = Includes,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                };
                var result = await _repository.GetAllByNameAsync(name, paginationSpecifications);

                if (result == null)
                    return ServiceResult<PaginationList<TEntity>>.Failure("No data found");
                else
                    return ServiceResult<PaginationList<TEntity>>.Success(result);
            }
            catch (Exception ex)
            {
                return ServiceResult<PaginationList<TEntity>>.Failure(ex.Message);
            }
        }

        public virtual async Task<ServiceResult<PaginationList<TEntityView>>> GetAllViewAsync(int pageNumber, int pageSize = 25)
        {
            try
            {
                PaginationSpecifications<TEntityView> paginationSpecifications = new PaginationSpecifications<TEntityView>()
                {
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                };
                var result = await _repository.GetAllViewAsync(paginationSpecifications);

                if (result == null)
                    return ServiceResult<PaginationList<TEntityView>>.Failure("No data found");
                else
                    return ServiceResult<PaginationList<TEntityView>>.Success(result);
            }
            catch (Exception ex)
            {
                return ServiceResult<PaginationList<TEntityView>>.Failure(ex.Message);
            }
        }

        public virtual async Task<ServiceResult<PaginationList<TEntityView>>> GetAllViewByAsync(TEntityView searchValues, int pageNumber, int pageSize = 25)
        {
            try
            {
                PaginationSpecifications<TEntityView> paginationSpecifications = new PaginationSpecifications<TEntityView>()
                {
                    SearchValue = Search.GenerateSearchExpression(searchValues),
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                };
                var result = await _repository.GetAllViewAsync(paginationSpecifications);

                if (result == null)
                    return ServiceResult<PaginationList<TEntityView>>.Failure("No data found");
                else
                    return ServiceResult<PaginationList<TEntityView>>.Success(result);
            }
            catch (Exception ex)
            {
                return ServiceResult<PaginationList<TEntityView>>.Failure(ex.Message);
            }
        }

        public virtual async Task<ServiceResult<IEnumerable<IdAndName>>> GetSelectListAsync(Expression<Func<TEntity, bool>>? searchValue = null)
        {
            RepositorySpecifications<TEntity> repositorySpecifications = new RepositorySpecifications<TEntity>()
            {
                SearchValue = searchValue,
                Includes = Includes,
            };
            var result = await _repository.GetAsSelectedItemsAsync(repositorySpecifications);

            if (result == null)
                return ServiceResult<IEnumerable<IdAndName>>.Failure("No data found");
            else
                return ServiceResult<IEnumerable<IdAndName>>.Success(result);
        }

        public virtual async Task<ServiceResult<TEntity?>> GetByIdAsync(Ulid id)
        {
            RepositorySpecifications<TEntity> repositorySpecifications = new RepositorySpecifications<TEntity>()
            {
                SearchValue = x => x.Id == id,
                Includes = Includes,
                IsTackable = true,
            };
            var result = await _repository.GetAsync(repositorySpecifications);

            if (result == null)
                return ServiceResult<TEntity?>.Failure("No data found");
            else
                return ServiceResult<TEntity?>.Success(result);
        }

        public async Task<ServiceResult<bool>> IsExistAsync(Ulid id)
        {
            var result = await _repository.IsExistAsync(id);

            if (result == false)
                return ServiceResult<bool>.Failure("Entity not found");
            else
                return ServiceResult<bool>.Success(result);
        }

        public virtual async Task<ServiceResult<TEntity?>> AddAsync(TEntity model)
        {
            try
            {
                EditModelBeforeSave(model);
                ValidateEntity(model, false);
                var result = await _repository.AddAsync(model);

                if (result == null)
                    return ServiceResult<TEntity?>.Failure("Failed save entity");
                else
                    return ServiceResult<TEntity?>.Success(result);
            }
            catch (Exception ex)
            {
                return ServiceResult<TEntity?>.Failure(ex.Message);
            }
        }

        public virtual async Task<ServiceResult<TEntity?>> UpdateAsync(TEntity model)
        {
            try
            {
                var entity = (await GetByIdAsync(model.Id)).Data;

                if (entity is null)
                    throw new NullReferenceException("Entity not found");

                entity = SetEntity(model, entity);

                EditModelBeforeSave(entity);
                ValidateEntity(entity, true);
                var result = await _repository.UpdateAsync(entity);

                if (result == null)
                    return ServiceResult<TEntity?>.Failure("Failed update entity");
                else
                    return ServiceResult<TEntity?>.Success(result);
            }
            catch (Exception ex)
            {
                return ServiceResult<TEntity?>.Failure(ex.Message);
            }
        }

        public virtual async Task<ServiceResult<bool>> RemoveAsync(Ulid id)
        {
            try
            {
                var entity = (await GetByIdAsync(id)).Data;

                if (entity is null)
                    throw new NullReferenceException("Entity not found");

                var result = await _repository.RemoveAsync(entity);

                if (result == null)
                    return ServiceResult<bool>.Failure("Failed delete entity");
                else
                    return ServiceResult<bool>.Success(true);
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure("Can't update entity");
            }
        }
        #endregion

        #region Sync
        public ServiceResult<IEnumerable<TEntity>> GetAll() => Task.Run(async () => await GetAllAsync()).GetAwaiter().GetResult();

        public ServiceResult<PaginationList<TEntity>> GetAllPaginationList(int pageNumber, int pageSize = 25) => Task.Run(async () => await GetAllPaginationListAsync(pageNumber, pageSize)).GetAwaiter().GetResult();

        public ServiceResult<PaginationList<TEntity>> GetAllByName(string name, int pageNumber, int pageSize = 25) => Task.Run(async () => await GetAllByNameAsync(name, pageNumber, pageSize)).GetAwaiter().GetResult();

        public ServiceResult<PaginationList<TEntityView>> GetAllView(int pageNumber, int pageSize = 25) => Task.Run(async () => await GetAllViewAsync(pageNumber, pageSize)).GetAwaiter().GetResult();

        public ServiceResult<PaginationList<TEntityView>> GetAllViewBy(TEntityView searchValues, int pageNumber, int pageSize = 25) => Task.Run(async () => await GetAllViewByAsync(searchValues, pageNumber, pageSize)).GetAwaiter().GetResult();

        public ServiceResult<IEnumerable<IdAndName>> GetSelectList(Expression<Func<TEntity, bool>>? searchValue = null) => Task.Run(async () => await GetSelectListAsync(searchValue)).GetAwaiter().GetResult();

        public ServiceResult<TEntity?> GetById(Ulid id) => Task.Run(async () => await GetByIdAsync(id)).GetAwaiter().GetResult();

        public ServiceResult<bool> IsExist(Ulid id) => Task.Run(async () => await IsExistAsync(id)).GetAwaiter().GetResult();

        public ServiceResult<TEntity?> Add(TEntity model) => Task.Run(async () => await AddAsync(model)).GetAwaiter().GetResult();

        public ServiceResult<TEntity?> Update(TEntity model) => Task.Run(async () => await UpdateAsync(model)).GetAwaiter().GetResult();

        public ServiceResult<bool> Remove(Ulid id) => Task.Run(async () => await RemoveAsync(id)).GetAwaiter().GetResult();
        #endregion
    }
}
