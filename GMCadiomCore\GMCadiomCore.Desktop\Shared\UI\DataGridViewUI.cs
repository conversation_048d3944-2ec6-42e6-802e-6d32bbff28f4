﻿namespace GMCadiomCore.Desktop.Shared.UI
{
    public class DataGridViewUI
    {


        //private Color BackColorSecond { get => Color.FromArgb(225, 155, 113); }
        //private Color ForeColorFirst { get => Color.FromArgb(45, 48, 71); }
        private RightToLeft IsRTL { get => RightToLeft.No; }

        public DataGridViewUI(DataGridView obj)
        {
            //obj.BackColor = ColorsSchema.DataGridView.BackColor;
            //obj.BackColor = ColorsSchema.DataGridView.ForeColor;
            obj.RightToLeft = ColorsSchema.IsRTL;

            obj.AllowUserToDeleteRows = false;
            obj.AllowUserToOrderColumns = false;
            obj.AllowUserToResizeColumns = true;
            obj.AllowUserToResizeRows = false;
            obj.EnableHeadersVisualStyles = false;
            obj.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            obj.RowHeadersVisible = false;
            obj.MultiSelect = false;
            obj.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            obj.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            obj.BorderStyle = BorderStyle.None;
            obj.CellBorderStyle = DataGridViewCellBorderStyle.SunkenHorizontal;

            obj.BackColor = ColorsSchema.DataGridView.BackColor;
            obj.ForeColor = ColorsSchema.DataGridView.ForeColor;
            obj.BackgroundColor = ColorsSchema.DataGridView.BackgroundColor;

            obj.AlternatingRowsDefaultCellStyle.BackColor = ColorsSchema.DataGridView.AlternatingRowsDefaultCellStyleBackColor;
            obj.AlternatingRowsDefaultCellStyle.ForeColor = ColorsSchema.DataGridView.AlternatingRowsDefaultCellStyleForeColor;
            obj.AlternatingRowsDefaultCellStyle.SelectionBackColor = ColorsSchema.DataGridView.AlternatingRowsDefaultCellStyleSelectionBackColor;
            obj.AlternatingRowsDefaultCellStyle.SelectionForeColor = ColorsSchema.DataGridView.AlternatingRowsDefaultCellStyleSelectionForeColor;

            obj.ColumnHeadersDefaultCellStyle.BackColor = ColorsSchema.DataGridView.ColumnHeadersDefaultCellStyleBackColor; // خلفيه خلايا الهيدير
            obj.ColumnHeadersDefaultCellStyle.ForeColor = ColorsSchema.DataGridView.ColumnHeadersDefaultCellStyleForeColor; // خط خلايا الهيدير
            obj.ColumnHeadersDefaultCellStyle.SelectionBackColor = ColorsSchema.DataGridView.ColumnHeadersDefaultCellStyleSelectionBackColor; // خط خلايا الهيدير
            obj.ColumnHeadersDefaultCellStyle.SelectionForeColor = ColorsSchema.DataGridView.ColumnHeadersDefaultCellStyleSelectionForeColor; // خط خلايا الهيدير

            obj.DefaultCellStyle.BackColor = ColorsSchema.DataGridView.DefaultCellStyleBackColor; // لون الخط بداخل الجدول
            obj.DefaultCellStyle.ForeColor = ColorsSchema.DataGridView.DefaultCellStyleForeColor; // لون الخط بداخل الجدول
            obj.DefaultCellStyle.SelectionBackColor = ColorsSchema.DataGridView.DefaultCellStyleSelectionBackColor; // لون الخلفيه للخلايا المحدده
            obj.DefaultCellStyle.SelectionForeColor = ColorsSchema.DataGridView.DefaultCellStyleSelectionForeColor; // لون الخط للخلايا المحدده

            obj.EnableHeadersVisualStyles = false;

            foreach (DataGridViewColumn col in obj.Columns)
                col.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;

            if (obj.Tag is string tagString && tagString != "Need")
            {
                obj.AllowUserToAddRows = false;
                obj.ReadOnly = true;
                obj.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            }
        }
    }
}
