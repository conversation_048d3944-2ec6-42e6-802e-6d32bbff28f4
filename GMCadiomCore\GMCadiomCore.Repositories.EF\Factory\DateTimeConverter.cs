﻿namespace GMCadiomCore.Repositories.EF.Factory
{
    public class DateTimeConverter : ValueConverter<DateTime, DateTime>
    {
        public DateTimeConverter() : this(null)
        {
        }

        public DateTimeConverter(ConverterMappingHints? mappingHints)
            : base(
                    convertToProviderExpression: x => x.ToUniversalTime(),
                    convertFromProviderExpression: x => DateTime.SpecifyKind(x, DateTimeKind.Utc).ToLocalTime())
        {
        }
    }
}
