﻿namespace GMCadiomCore.Repositories.Factory
{
    public enum DataBaseEntityState
    {
        Detached = 0,
        Unchanged = 1,
        Deleted = 2,
        Modified = 3,
        Added = 4,
    }

    public class DataBaseEntity
    {
        [JsonConverter(typeof(ObjectToJsonConverter))]
        public object Entity { get; set; } // Database entity model
        public string EntityType { get; set; } // Stores the entity type
        public DataBaseEntityState EntityState { get; set; }
    }

    // Custom converter to serialize/deserialize 'Entity' dynamically
    public class ObjectToJsonConverter : JsonConverter
    {
        public override bool CanConvert(Type objectType) => objectType == typeof(object);

        public override object? ReadJson(JsonReader reader, Type objectType, object? existingValue, JsonSerializer serializer)
        {
            JObject obj = JObject.Load(reader);
            string? typeName = obj["EntityType"]?.ToString();

            if (string.IsNullOrEmpty(typeName))
                return null;

            Type? entityType = Type.GetType(typeName);
            if (entityType == null)
                throw new InvalidOperationException($"Type '{typeName}' could not be found.");

            return obj["Entity"]?.ToObject(entityType, serializer);
        }

        public override void WriteJson(JsonWriter writer, object? value, JsonSerializer serializer)
        {
            if (value is null)
                return;

            object entity = value as object;

            JObject obj = new JObject
            {
                { "EntityType", value?.GetType().AssemblyQualifiedName },
                { "Entity", JToken.FromObject(entity, serializer) }
            };
            obj.WriteTo(writer);
        }
    }
}
