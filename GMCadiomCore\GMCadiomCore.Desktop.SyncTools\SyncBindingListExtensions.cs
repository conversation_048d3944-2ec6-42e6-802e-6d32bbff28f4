﻿namespace GMCadiomCore.Desktop.SyncTools
{
    public static class SyncBindingListExtensions
    {
        public static SyncBindingList<T> GetNewBindable<T>(this SyncBindingList<T> source, Func<T, bool> predicate, string keyValue)
        {
            List<T> filteredList = source.Where(predicate).ToList();
            SyncBindingList<T> newList = new SyncBindingList<T>(filteredList);
            source.ListChanged += (s, e) => Source_ListChanged(s, e, source, newList, keyValue);
            return newList;
        }

        public static SyncBindingList<T> Paganate<T>(this SyncBindingList<T> source, int skip, int take, string keyValue)
        {
            List<T> filteredList = source.Skip(skip).Take(take).ToList();
            SyncBindingList<T> newList = new SyncBindingList<T>(filteredList);
            source.ListChanged += (s, e) => Source_ListChanged(s, e, source, newList, keyValue);
            return newList;
        }

        private static void Source_ListChanged<T>(object? sender, ListChangedEventArgs listChanged, SyncBindingList<T> source, SyncBindingList<T> newList, string keyValue)
        {
            T item = source[listChanged.NewIndex];
            if (item != null)
            {
                switch (listChanged.ListChangedType)
                {
                    case ListChangedType.Reset:
                        break;
                    case ListChangedType.ItemAdded:
                        newList.Add(item);
                        break;
                    case ListChangedType.ItemDeleted:
                        newList.Remove(item);
                        break;
                    case ListChangedType.ItemMoved:
                        break;
                    case ListChangedType.ItemChanged:
                        if (!string.IsNullOrEmpty(keyValue))
                        {
                            Type type = typeof(T);
                            PropertyInfo? propertyInfo = type.GetProperties().Where(x => x.Name == keyValue).FirstOrDefault();
                            if (propertyInfo != null)
                            {
                                var value = propertyInfo.GetValue(item);
                                if (value != null)
                                {
                                    ParameterExpression parameterExpression = Expression.Parameter(typeof(T), nameof(T));
                                    MemberExpression memberExpression = Expression.Property(parameterExpression, propertyInfo.Name);
                                    ConstantExpression constantExpression = Expression.Constant(value, typeof(int));
                                    BinaryExpression equalBinaryExpression = Expression.Equal(memberExpression, constantExpression);
                                    Func<T, bool> isEqualExpression = Expression.Lambda<Func<T, bool>>(equalBinaryExpression, parameterExpression).Compile();

                                    T? oldItem = newList.SingleOrDefault(isEqualExpression);
                                    if (oldItem != null)
                                    {
                                        int index = newList.IndexOf(oldItem);
                                        newList[index] = item;
                                    }
                                    else
                                    {

                                    }
                                }
                            }
                        }
                        break;
                    case ListChangedType.PropertyDescriptorAdded:
                        break;
                    case ListChangedType.PropertyDescriptorDeleted:
                        break;
                    case ListChangedType.PropertyDescriptorChanged:
                        break;
                }
            }
        }
    }
}
