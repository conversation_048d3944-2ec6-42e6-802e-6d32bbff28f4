<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs">

  <Package Name="LeadTeams Client"
           Language="1033"
           Version="1.7.0"
           Manufacturer="LeadTeams"
           UpgradeCode="8A9744BA-5304-4750-A664-11ABB210C55A"
           InstallerVersion="200"
           ProductCode="04F0D950-CCE4-4B9A-AA87-9C6465CA155C">

    <!-- Upgrade logic to replace existing installations -->
    <MajorUpgrade DowngradeErrorMessage="A newer version of [ProductName] is already installed."
                  Schedule="afterInstallInitialize"
                  AllowSameVersionUpgrades="yes" />

    <!-- Media definition -->
    <Media Id="1" Cabinet="LeadTeamsClient.cab" EmbedCab="yes" />

    <!-- Installation directory structure -->
    <StandardDirectory Id="ProgramFiles64Folder">
      <Directory Id="ManufacturerFolder" Name="LeadTeams">
        <Directory Id="INSTALLFOLDER" Name="LeadTeams Client" />
      </Directory>
    </StandardDirectory>
    <StandardDirectory Id="ProgramMenuFolder">
      <Directory Id="ApplicationProgramsFolder" Name="LeadTeams Client" />
    </StandardDirectory>
    <StandardDirectory Id="DesktopFolder" />

    <!-- Main feature -->
    <Feature Id="ProductFeature" Title="LeadTeams Client" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
      <ComponentRef Id="ApplicationShortcut" />
      <ComponentRef Id="DesktopShortcut" />
    </Feature>

    <!-- Application shortcuts -->
    <Component Id="ApplicationShortcut" Directory="ApplicationProgramsFolder">
      <Shortcut Id="ApplicationStartMenuShortcut"
                Name="LeadTeams Client"
                Description="LeadTeams Client Application"
                Target="[INSTALLFOLDER]LeadTeams.Client.exe"
                WorkingDirectory="INSTALLFOLDER"
                Icon="LeadTeamsIcon.ico" />
      <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall" />
      <RegistryValue Root="HKCU" Key="Software\LeadTeams\Client" Name="installed" Type="integer" Value="1" KeyPath="yes" />
    </Component>

    <Component Id="DesktopShortcut" Directory="DesktopFolder">
      <Shortcut Id="ApplicationDesktopShortcut"
                Name="LeadTeams Client"
                Description="LeadTeams Client Application"
                Target="[INSTALLFOLDER]LeadTeams.Client.exe"
                WorkingDirectory="INSTALLFOLDER"
                Icon="LeadTeamsIcon.ico" />
      <RegistryValue Root="HKCU" Key="Software\LeadTeams\Client" Name="desktop_shortcut" Type="integer" Value="1" KeyPath="yes" />
    </Component>

    <!-- Icon definition -->
    <Icon Id="LeadTeamsIcon.ico" SourceFile="LeadTeamsIcon.ico" />

    <!-- Add/Remove Programs properties -->
    <Property Id="ARPPRODUCTICON" Value="LeadTeamsIcon.ico" />
    <Property Id="ARPHELPLINK" Value="https://leadteams.com" />
    <Property Id="ARPURLINFOABOUT" Value="https://leadteams.com" />
    <Property Id="ARPNOREPAIR" Value="1" />
    <Property Id="ARPNOMODIFY" Value="1" />

    <!-- Windows Apps integration -->
    <Property Id="ALLUSERS" Value="1" />

  </Package>
</Wix>
