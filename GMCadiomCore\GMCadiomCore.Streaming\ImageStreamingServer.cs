﻿namespace GMCadiomCore.Streaming
{
    /// <summary>
    /// Provides a streaming server that can be used to stream any images source
    /// to any client.
    /// </summary>
    public class ImageStreamingServer : IDisposable
    {
        private List<Socket> _Clients;
        private Thread _Thread;

        public ImageStreamingServer() : this(ScreenShot.Snapshots(600, 450, 600, 450, true))
        {

        }

        public ImageStreamingServer(int PrimaryScreenWidth, int PrimaryScreenHeight, int width, int height, bool showCursor) : this(ScreenShot.Snapshots(PrimaryScreenWidth, PrimaryScreenHeight, width, height, showCursor))
        {

        }

        public ImageStreamingServer(IEnumerable<Image> imagesSource)
        {
            _Clients = new List<Socket>();
            _Thread = null!;
            ImagesSource = imagesSource;
            Interval = 50;
        }

        /// <summary>
        /// Gets or sets the source of images that will be streamed to the 
        /// any connected client.
        /// </summary>
        public IEnumerable<Image> ImagesSource { get; set; }

        /// <summary>
        /// Gets or sets the interval in milliseconds (or the delay time) between 
        /// the each image and the other of the stream (the default is . 
        /// </summary>
        public int Interval { get; set; }

        /// <summary>
        /// Gets a collection of client sockets.
        /// </summary>
        public IEnumerable<Socket> Clients { get { return _Clients; } }

        /// <summary>
        /// Returns the status of the server. True means the server is currently 
        /// running and ready to serve any client requests.
        /// </summary>
        public bool IsRunning { get { return _Thread != null && _Thread.IsAlive; } }

        /// <summary>
        /// Starts the server to accepts any new connections on the specified port.
        /// </summary>
        /// <param name="port"></param>
        public void Start(int port)
        {
            lock (this)
            {
                _Thread = new Thread(new ParameterizedThreadStart(ServerThread));
                _Thread.IsBackground = true;
                _Thread.Start(port);
            }
        }

        /// <summary>
        /// Starts the server to accepts any new connections on the default port (8080).
        /// </summary>
        public void Start()
        {
            Start(8080);
        }

        public void Stop()
        {
            if (IsRunning)
            {
                try
                {
                    _Thread.Join();
                    _Thread.Interrupt();
                }
                finally
                {
                    lock (_Clients)
                    {
                        foreach (var s in _Clients)
                        {
                            try
                            {
                                s.Close();
                            }
                            catch { }
                        }
                        _Clients.Clear();
                    }
                    _Thread = null!;
                }
            }
        }

        /// <summary>
        /// This the main thread of the server that serves all the new 
        /// connections from clients.
        /// </summary>
        /// <param name="state"></param>
        private void ServerThread(object? state)
        {
            try
            {
                if (state != null)
                {
                    Socket Server = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
                    Server.Bind(new IPEndPoint(IPAddress.Any, (int)state));
                    Server.Listen(10);

                    Debug.WriteLine(string.Format("Server started on port {0}.", state));

                    foreach (Socket client in Server.IncommingConnectoins())
                        ThreadPool.QueueUserWorkItem(new WaitCallback(ClientThread), client);
                }
            }
            catch { }

            Stop();
        }

        /// <summary>
        /// Each client connection will be served by this thread.
        /// </summary>
        /// <param name="client"></param>
        private void ClientThread(object? client)
        {
            if (client != null && client is Socket socket)
            {
                Debug.WriteLine(string.Format("New client from {0}", socket.RemoteEndPoint?.ToString()));

                lock (_Clients)
                    _Clients.Add(socket);

                try
                {
                    using (MjpegWriter wr = new MjpegWriter(new NetworkStream(socket, true)))
                    {
                        // Writes the response header to the client.
                        wr.WriteHeader();

                        // Streams the images from the source to the client.
                        foreach (var imgStream in ImagesSource.Streams())
                        {
                            if (Interval > 0)
                                Thread.Sleep(Interval);

                            wr.Write(imgStream);
                        }
                    }
                }
                catch { }
                finally
                {
                    lock (_Clients)
                        _Clients.Remove(socket);
                }
            }
        }

        public void Dispose()
        {
            Stop();
        }
    }
}
