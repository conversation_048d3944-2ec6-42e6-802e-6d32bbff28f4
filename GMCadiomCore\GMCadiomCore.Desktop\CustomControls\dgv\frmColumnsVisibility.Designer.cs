﻿namespace GMCadiomCore.Desktop.CustomControls.dgv
{
    partial class frmColumnsVisibility
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tlpMain = new TableLayoutPanel();
            lblHeader = new Label();
            clbxColumns = new CheckedListBox();
            tlpMain.SuspendLayout();
            SuspendLayout();
            // 
            // tlpMain
            // 
            tlpMain.ColumnCount = 1;
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20F));
            tlpMain.Controls.Add(lblHeader, 0, 0);
            tlpMain.Controls.Add(clbxColumns, 0, 1);
            tlpMain.Dock = DockStyle.Fill;
            tlpMain.Location = new Point(0, 0);
            tlpMain.Name = "tlpMain";
            tlpMain.RowCount = 2;
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Absolute, 40F));
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tlpMain.Size = new Size(284, 261);
            tlpMain.TabIndex = 0;
            // 
            // lblHeader
            // 
            lblHeader.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            lblHeader.AutoSize = true;
            lblHeader.Location = new Point(3, 12);
            lblHeader.Name = "lblHeader";
            lblHeader.Size = new Size(278, 15);
            lblHeader.TabIndex = 0;
            lblHeader.Text = "Select Columns Visibility";
            lblHeader.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // clbxColumns
            // 
            clbxColumns.Dock = DockStyle.Fill;
            clbxColumns.FormattingEnabled = true;
            clbxColumns.Location = new Point(3, 43);
            clbxColumns.Name = "clbxColumns";
            clbxColumns.Size = new Size(278, 215);
            clbxColumns.TabIndex = 1;
            // 
            // frmColumnsVisibility
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(284, 261);
            Controls.Add(tlpMain);
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "frmColumnsVisibility";
            ShowInTaskbar = false;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "Select Columns Visibility";
            tlpMain.ResumeLayout(false);
            tlpMain.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private TableLayoutPanel tlpMain;
        private Label lblHeader;
        private CheckedListBox clbxColumns;
    }
}
