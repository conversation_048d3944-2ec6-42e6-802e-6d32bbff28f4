﻿namespace GMCadiomCore.Models.ModelValidation.CustomAttribute
{
    [AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
    public class CustomRequiredAttribute : RequiredAttribute
    {
        private readonly string _propName;

        public CustomRequiredAttribute(string displayName = "", [CallerMemberName] string? callerName = null)
        {
            _propName = !string.IsNullOrWhiteSpace(displayName) ? displayName : callerName ?? throw new ArgumentNullException(nameof(callerName));
        }

        public override bool IsValid(object? value)
        {
            switch (value)
            {
                case null:
                    ErrorMessage = $"Field '{_propName}' is required.";
                    return false;
                case string strValue when string.IsNullOrWhiteSpace(strValue):
                    ErrorMessage = $"Field '{_propName}' cannot be empty.";
                    return false;
                case int or long or float or double or decimal when Convert.ToDecimal(value) <= 0:
                    ErrorMessage = $"Field '{_propName}' must be greater than zero.";
                    return false;
                case DateTime dateTimeValue when dateTimeValue == default:
                    ErrorMessage = $"Field '{_propName}' must be a valid date.";
                    return false;
                case Guid guidValue when guidValue == Guid.Empty:
                    ErrorMessage = $"Field '{_propName}' must be a valid GUID.";
                    return false;
                case Ulid ulidValue when ulidValue == Ulid.Empty:
                    ErrorMessage = $"Field '{_propName}' must be a valid ULID.";
                    return false;
                case IEnumerable collection when !collection.Cast<object>().Any():
                    ErrorMessage = $"Field '{_propName}' must contain at least one item.";
                    return false;
            }

            return base.IsValid(value);
        }
    }
}
