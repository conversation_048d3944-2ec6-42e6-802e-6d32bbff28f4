﻿namespace GMCadiomCore.Presenters.BasePresenter
{
    public class BaseReportPresenter<T1> : BaseReportPresenter, IBaseReportPresenter<T1> where T1 : class
    {
        //Fields
        public new IBaseReport<T1> View { get; private set; }
        public BindingList<T1> MainList { get; protected set; }
        public T1 CurrentModel { get; private set; }

        //Constructor
        public BaseReportPresenter(IBaseReport<T1> view, IBaseUnitOfWork repository, bool isDialog = false)
            : base(view, repository, isDialog)
        {
            View = view;
            //Subscribe Event Handler Methods To View Events
            View.ShowEvent += (s, e) => SearchAction();
            View.SearchEvent += (s, e) => SearchAction();
            View.ExportEvent += (s, e) => ExportAction();
            //Set Bindind Source
            View.SetBindingSource(MainBindingSource);
            //Load List View
            LoadAllActionList();
            //Show view
            if (isDialog)
                View.ShowDialog();
            else
                View.Show();
        }

        //Methods
        public new virtual void MainData()
        {
        }

        public new virtual void LoadAllActionList()
        {
            MainData();
            MainBindingSource.DataSource = MainList;
        }

        public new virtual void ImportAction()
        {
        }

        public new virtual void ExportAction()
        {
        }

        public new virtual void SearchAction()
        {
            MainData();
            //TODO:
            //MainList = MainList.GenericSearch(View.ViewModel);
            MainBindingSource.DataSource = MainList;
        }
    }

    public class BaseReportPresenter : IBaseReportPresenter
    {
        //Fields
        public IBaseReport View { get; private set; }
        public IBaseUnitOfWork Repository { get; private set; }
        public BindingSource MainBindingSource { get; private set; }

        //Constructor
        public BaseReportPresenter(IBaseReport view, IBaseUnitOfWork repository, bool isDialog = false)
        {
            MainBindingSource = new BindingSource();
            View = view;
            Repository = repository;
            //Subscribe Event Handler Methods To View Events
            View.ShowEvent += (s, e) => SearchAction();
            View.SearchEvent += (s, e) => SearchAction();
            View.ExportEvent += (s, e) => ExportAction();
            //Load List View
            LoadAllActionList();
            //Show view
            if (isDialog)
                View.ShowDialog();
            else
                View.Show();
        }

        //Methods
        public virtual void MainData()
        {
        }

        public virtual void LoadAllActionList()
        {
            MainData();
        }

        public virtual void ImportAction()
        {
        }

        public virtual void ExportAction()
        {
        }

        public virtual void SearchAction()
        {
            MainData();
        }
    }
}
