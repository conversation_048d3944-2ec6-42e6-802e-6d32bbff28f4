﻿<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs">
  <Fragment>

    <!-- Component group for all application files -->
    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">

      <!-- Main executable -->
      <Component Id="MainExecutable">
        <File Id="LeadTeamsDesktopExe" Source="$(var.LeadTeams.Desktop.TargetPath)" KeyPath="yes" />
      </Component>

      <!-- Application icon -->
      <Component Id="ApplicationIcon">
        <File Id="ApplicationIconFile" Source="$(var.LeadTeams.Desktop.TargetDir)icons8-monitoring-96.ico" KeyPath="yes" />
      </Component>

      <!-- Configuration files -->
      <Component Id="ConfigFiles">
        <File Id="AppConfig" Source="$(var.LeadTeams.Desktop.TargetDir)App.config" KeyPath="yes" />
        <File Id="RuntimeConfig" Source="$(var.LeadTeams.Desktop.TargetDir)LeadTeams.Desktop.runtimeconfig.json" />
        <File Id="DepsJson" Source="$(var.LeadTeams.Desktop.TargetDir)LeadTeams.Desktop.deps.json" />
      </Component>

      <!-- Application DLL -->
      <Component Id="ApplicationDll">
        <File Id="LeadTeamsDesktopDll" Source="$(var.LeadTeams.Desktop.TargetDir)LeadTeams.Desktop.dll" KeyPath="yes" />
      </Component>

      <!-- Debug symbols (optional) -->
      <Component Id="DebugSymbols">
        <File Id="LeadTeamsDesktopPdb" Source="$(var.LeadTeams.Desktop.TargetDir)LeadTeams.Desktop.pdb" KeyPath="yes" />
      </Component>

      <!-- This will be expanded to include all necessary runtime dependencies -->
      <!-- Use Heat.exe to harvest all files from the output directory -->

    </ComponentGroup>

  </Fragment>
</Wix>
