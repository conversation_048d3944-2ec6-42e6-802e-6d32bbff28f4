﻿namespace GMCadiomCore.Models.Model
{
    [Table("ScreensAccessProfile")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<BaseScreensAccessProfileModel>))]
    public class BaseScreensAccessProfileModel : BaseModel
    {
        private string _ScreensAccessProfileName;

        [CustomRequired]
        [DisplayName("Screens Access Profile Name")]
        [MaxLength(100)]
        [Column(TypeName = "nvarchar(max)")]
        [Searchable(SearchableAttribute.SelectControls.TextBox, "")]
        public string ScreensAccessProfileName { get => _ScreensAccessProfileName; set => CheckPropertyChanged(ref _ScreensAccessProfileName, ref value); }

        [DisplayName("Screens")]
        public virtual ICollection<BaseScreensAccessProfileDetailsModel> ScreensAccessProfileDetails { get; set; } = new List<BaseScreensAccessProfileDetailsModel>();

        [Browsable(false)]
        public virtual ICollection<BaseUserModel> Users { get; set; } = new List<BaseUserModel>();
    }
}
