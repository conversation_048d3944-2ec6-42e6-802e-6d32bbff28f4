﻿namespace GMCadiomCore.Shared.Extensions
{
    public static class ReflectionExtensions
    {
        private static Assembly[]? _ProjectAssemblies = null;
        private static Assembly[] ProjectAssemblies
        {
            get
            {
                if (_ProjectAssemblies == null)
                {
                    List<Assembly> assemblies = AppDomain.CurrentDomain.GetAssemblies().ToList();
                    IEnumerable<AssemblyName>? referencedAssemblies = Assembly.GetEntryAssembly()?
                        .GetReferencedAssemblies()
                        .Where(a => assemblies.All(loaded => loaded.GetName().Name != a.Name));
                    if (referencedAssemblies != null)
                    {
                        foreach (var assemblyName in referencedAssemblies)
                        {
                            assemblies.Add(AppDomain.CurrentDomain.Load(assemblyName));
                        }
                        _ProjectAssemblies = assemblies.Where(x => x.FullName != null && !x.FullName.Contains("System") && !x.FullName.Contains("Microsoft")).ToArray();
                    }

                    if (_ProjectAssemblies == null)
                        _ProjectAssemblies = Array.Empty<Assembly>();
                }
                return _ProjectAssemblies;
            }
        }

        private static Type[]? _ProjectTypes;
        private static Type[] ProjectTypes
        {
            get
            {
                if (_ProjectTypes == null)
                    _ProjectTypes = ProjectAssemblies.GetTypesFromAssembly();
                return _ProjectTypes;
            }
        }

        public static T? GetAttribute<T>(this MemberInfo member, bool isRequired) where T : Attribute
        {
            var attribute = member.GetCustomAttributes(typeof(T), false).FirstOrDefault();

            if (attribute == null && isRequired)
            {
                throw new ArgumentException(
                    string.Format(
                        CultureInfo.InvariantCulture,
                        "The {0} attribute must be defined on member {1}",
                        typeof(T).Name,
                        member.Name));
            }
            return (T?)attribute;
        }

        public static string GetDisplayNameValue(this MemberInfo member)
        {
            var attributes = member.GetAttribute<DisplayNameAttribute>(false);
            if (attributes != null)
                return attributes.DisplayName;
            return "";
        }

        public static string GetTableNameValue(this Type type)
        {
            var attributes = type.GetAttribute<TableAttribute>(false);
            if (attributes != null)
                return attributes.Name;
            return type.Name.Replace("Model", "");
        }

        public static Assembly[]? GetAssembliesByAssemblyName(string AssemblyName)
        {
            Assembly[]? Assemblies = ProjectAssemblies?.Where(x => x.FullName != null && x.FullName.Contains(AssemblyName)).ToArray();
            return Assemblies;
        }

        public static Type[] GetTypesFromAssembly(this Assembly[] assemblies)
        {
            List<Type> types = new List<Type>();
            foreach (Assembly assembly in assemblies)
            {
                try
                {
                    Type[] type = assembly.GetTypes();
                    if (type is not null)
                        types.AddRange(type);
                }
                catch
                {
                    continue;
                }
            }
            return types.ToArray();
        }

        public static Type? GetTypeFromAssemblyByName(this Type[] types, string TypeName)
        {
            Type? type = types.Where(x => x.Name == TypeName).FirstOrDefault();
            return type;
        }

        public static Type? GetTypeFromAssemblyByName(string AssemblyName, string TypeName)
        {
            Assembly[]? assemblies = GetAssembliesByAssemblyName(AssemblyName);
            Type[]? types = assemblies?.GetTypesFromAssembly();
            Type? type = types?.Where(x => x.Name == TypeName).FirstOrDefault();
            return type;
        }

        public static Type? GetTypeFromAssemblyByName(string TypeName)
        {
            Type? type = ProjectTypes?.Where(x => x.Name == TypeName).FirstOrDefault();
            return type;
        }
    }
}
