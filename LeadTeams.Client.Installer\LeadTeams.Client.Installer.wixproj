<Project Sdk="WixToolset.Sdk/6.0.1">
  <PropertyGroup>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
  </PropertyGroup>
  <PropertyGroup>
    <OutputName>LeadTeamsClientSetup</OutputName>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <DefineConstants>Debug</DefineConstants>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Product.wxs" />
    <Compile Include="Components.wxs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="LeadTeamsIcon.ico" />
    <Content Include="License.rtf" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\LeadTeams.Client\LeadTeams.Client.csproj">
      <Name>LeadTeams.Client</Name>
      <Project>{4b0b2804-d1a0-47df-90d1-17bc58d55737}</Project>
      <Private>True</Private>
      <DoNotHarvest>True</DoNotHarvest>
      <RefProjectOutputGroups>Binaries;Content;Satellites</RefProjectOutputGroups>
      <RefTargetDir>INSTALLFOLDER</RefTargetDir>
    </ProjectReference>
  </ItemGroup>
</Project>
