﻿// <auto-generated />
using System;
using LeadTeams.Repositories.EF.Factory.Contexts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace LeadTeams.Repositories.EF.Migrations.SQLiteMigrations
{
    [DbContext(typeof(SQLiteApplicationDBContext))]
    [Migration("20250725134639_LeadTeams_003")]
    partial class LeadTeams_003
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.14");

            modelBuilder.Entity("LeadTeams.Models.Model.AllowanceModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<string>("EmployeeAllowanceDuration")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT");

                    b.Property<string>("EmployeeAllowanceName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId");

                    b.ToTable("Allowance");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.AskLeaveModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<string>("AskLeaveDescription")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("AskLeaveEndDate")
                        .HasColumnType("Date");

                    b.Property<byte[]>("AskLeaveRequestedById")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<DateTime>("AskLeaveStartDate")
                        .HasColumnType("Date");

                    b.Property<string>("AskLeaveStatus")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("AskLeaveStatusAt")
                        .HasColumnType("Date");

                    b.Property<byte[]>("AskLeaveStatusById")
                        .HasColumnType("BLOB");

                    b.Property<string>("AskLeaveSubject")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("AskLeaveType")
                        .IsRequired()
                        .HasMaxLength(25)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("AskLeaveRequestedById");

                    b.HasIndex("AskLeaveStatusById");

                    b.HasIndex("OrganizationId");

                    b.ToTable("AskLeave");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.AttendanceLogModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime>("AttendanceLogDateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("AttendanceLogEmployeeShift")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("AttendanceLogIPAddress")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("AttendanceLogIPAddressType")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT");

                    b.Property<string>("AttendanceLogStatus")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("TEXT");

                    b.Property<string>("AttendanceLogTimeZone")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("EmployeeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("TaskId")
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("OrganizationId");

                    b.HasIndex("TaskId");

                    b.ToTable("AttendanceLog");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.AuditModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<string>("AffectedColumns")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("EmployeeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<string>("NewValues")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("OldValues")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<string>("PrimaryKey")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("TableName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("OrganizationId");

                    b.ToTable("Audit");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.EmployeeAllowanceModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("AllowanceId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("EmployeeId")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<decimal>("EmployeeAllowanceAmount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id", "AllowanceId", "EmployeeId");

                    b.HasIndex("AllowanceId");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("OrganizationId");

                    b.ToTable("EmployeeAllowance");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.EmployeeEducationalQualificationModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<string>("EmployeeEducationalQualificationGraduationClass")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("EmployeeEducationalQualificationGraduationImage")
                        .HasColumnType("BLOB");

                    b.Property<string>("EmployeeEducationalQualificationGraduationYear")
                        .IsRequired()
                        .HasMaxLength(4)
                        .HasColumnType("TEXT");

                    b.Property<string>("EmployeeEducationalQualificationName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("EmployeeEducationalQualificationSource")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("EmployeeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("OrganizationId");

                    b.ToTable("EmployeeEducationalQualification");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.EmployeeKidsModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("EmployeeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<string>("EmployeeKidsAddress")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("EmployeeKidsDateOfBirth")
                        .HasColumnType("Date");

                    b.Property<string>("EmployeeKidsGender")
                        .IsRequired()
                        .HasMaxLength(6)
                        .HasColumnType("TEXT");

                    b.Property<string>("EmployeeKidsName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("OrganizationId");

                    b.ToTable("EmployeeKids");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.EmployeeManagerModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("ManagerId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("EmployeeId")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id", "ManagerId", "EmployeeId");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("ManagerId");

                    b.HasIndex("OrganizationId");

                    b.ToTable("EmployeeManager");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.EmployeeModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<string>("EmployeeAddress")
                        .HasColumnType("TEXT");

                    b.Property<bool>("EmployeeCanTravel")
                        .HasColumnType("INTEGER");

                    b.Property<string>("EmployeeContact")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("EmployeeCurrency")
                        .HasMaxLength(3)
                        .HasColumnType("TEXT");

                    b.Property<string>("EmployeeCustomID")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("EmployeeDateOfBirth")
                        .HasColumnType("Date");

                    b.Property<string>("EmployeeGender")
                        .HasMaxLength(6)
                        .HasColumnType("TEXT");

                    b.Property<bool>("EmployeeIsOnline")
                        .HasColumnType("INTEGER");

                    b.Property<string>("EmployeeJobDescription")
                        .HasColumnType("TEXT");

                    b.Property<string>("EmployeeJobSpecification")
                        .HasColumnType("TEXT");

                    b.Property<string>("EmployeeJobTitle")
                        .HasMaxLength(35)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("EmployeeJoinDate")
                        .HasColumnType("Date");

                    b.Property<string>("EmployeeMaritalStatus")
                        .HasMaxLength(15)
                        .HasColumnType("TEXT");

                    b.Property<string>("EmployeeName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("EmployeeSalary")
                        .HasPrecision(18, 5)
                        .HasColumnType("decimal(18,2)");

                    b.Property<short>("EmployeeYearsOfExperience")
                        .HasColumnType("smallint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("ScreensAccessProfileId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("ShiftId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("UserTypeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId");

                    b.HasIndex("ScreensAccessProfileId");

                    b.HasIndex("ShiftId");

                    b.ToTable("Employee");

                    b.HasData(
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 100, 130, 239, 252, 239, 83, 81, 221, 247, 196, 135 },
                            EmployeeCanTravel = false,
                            EmployeeCurrency = "EGP",
                            EmployeeCustomID = "EMP00001",
                            EmployeeDateOfBirth = new DateTime(2000, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            EmployeeGender = "Male",
                            EmployeeIsOnline = false,
                            EmployeeJoinDate = new DateTime(2000, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            EmployeeName = "Demo Admin",
                            EmployeeSalary = 10000m,
                            EmployeeYearsOfExperience = (short)0,
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            Password = "123456",
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 44, 62, 114, 205, 124, 191, 187, 231, 209, 195 },
                            UserName = "<EMAIL>",
                            UserTypeId = new byte[] { 1, 149, 151, 18, 237, 151, 125, 98, 231, 63, 22, 85, 66, 112, 24, 40 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 100, 131, 181, 12, 255, 167, 182, 132, 218, 190, 27 },
                            EmployeeCanTravel = false,
                            EmployeeCurrency = "EGP",
                            EmployeeCustomID = "EMP00002",
                            EmployeeDateOfBirth = new DateTime(2000, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            EmployeeGender = "Male",
                            EmployeeIsOnline = false,
                            EmployeeJoinDate = new DateTime(2000, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            EmployeeName = "Demo User",
                            EmployeeSalary = 4000m,
                            EmployeeYearsOfExperience = (short)0,
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            Password = "123456",
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 249, 194, 128, 176, 2, 72, 168, 14, 186, 109 },
                            UserName = "<EMAIL>",
                            UserTypeId = new byte[] { 1, 149, 151, 19, 36, 141, 8, 106, 78, 226, 193, 68, 147, 30, 95, 157 }
                        });
                });

            modelBuilder.Entity("LeadTeams.Models.Model.LoginSessionModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<string>("DeviceId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("EmployeeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<string>("SessionAppName")
                        .IsRequired()
                        .HasMaxLength(25)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("SessionDateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("SessionHostType")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("SessionLastDateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("SessionStatus")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT");

                    b.Property<string>("SessionToken")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("OrganizationId");

                    b.ToTable("LoginSession");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ManagementTeamEmployeeModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("EmployeeId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("ManagementTeamId")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id", "EmployeeId", "ManagementTeamId");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("ManagementTeamId");

                    b.HasIndex("OrganizationId");

                    b.ToTable("ManagementTeamEmployee");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ManagementTeamManagerModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("ManagerId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("ManagementTeamId")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id", "ManagerId", "ManagementTeamId");

                    b.HasIndex("ManagementTeamId");

                    b.HasIndex("ManagerId");

                    b.HasIndex("OrganizationId");

                    b.ToTable("ManagementTeamManager");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ManagementTeamModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<string>("TeamName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId");

                    b.ToTable("ManagementTeam");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.MeetingEmployeeModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("EmployeeId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("MeetingId")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id", "EmployeeId", "MeetingId");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("MeetingId");

                    b.HasIndex("OrganizationId");

                    b.ToTable("MeetingEmployee");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.MeetingModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("MeetingDescription")
                        .HasColumnType("TEXT");

                    b.Property<string>("MeetingName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("MeetingOtherName")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("MeetingStartDate")
                        .HasColumnType("Date");

                    b.Property<TimeSpan>("MeetingStartTime")
                        .HasColumnType("Time");

                    b.Property<DateTime?>("MeetingStopDate")
                        .HasColumnType("Date");

                    b.Property<TimeSpan?>("MeetingStopTime")
                        .HasColumnType("Time");

                    b.Property<bool>("MeetingWithEmployees")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("ProjectId")
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId");

                    b.HasIndex("ProjectId");

                    b.ToTable("Meeting");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.MessageModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("EmployeeFromId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("EmployeeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("EmployeeToId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("MessageContent")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<DateTime>("MessageDate")
                        .HasColumnType("datetime");

                    b.Property<bool>("MessageIsRead")
                        .HasColumnType("INTEGER");

                    b.Property<string>("MessageMediaMimeType")
                        .HasColumnType("TEXT");

                    b.Property<int>("MessageType")
                        .HasColumnType("INTEGER");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeFromId");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("EmployeeToId");

                    b.HasIndex("OrganizationId");

                    b.ToTable("Message");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.OrganizationModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<string>("OrganizationAddress")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("OrganizationContact")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("OrganizationEmail")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("OrganizationName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("OrganizationPassword")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("OrganizationWebsite")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Organization");

                    b.HasData(
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            IsActive = true,
                            OrganizationEmail = "<EMAIL>",
                            OrganizationName = "Demo Sherif Shalaby",
                            OrganizationPassword = "123456"
                        });
                });

            modelBuilder.Entity("LeadTeams.Models.Model.OrganizationNewsModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<DateTime>("OrganizationNewsDateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("OrganizationNewsDescription")
                        .HasColumnType("TEXT");

                    b.Property<string>("OrganizationNewsTitle")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId");

                    b.ToTable("OrganizationNews");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ProjectModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<string>("ProjectDescription")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProjectName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId");

                    b.ToTable("Project");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.RefreshTokenModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("ExpiresOn")
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("RevokedOn")
                        .HasColumnType("TEXT");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("UserId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId");

                    b.HasIndex("UserId");

                    b.ToTable("RefreshTokenModel");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ScreenShotsMonitoringModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("EmployeeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<DateTime>("ScreenShotsMonitoringDateTime")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ScreenShotsMonitoringImage")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<string>("ScreenShotsMonitoringImageName")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("TEXT");

                    b.Property<string>("ScreenShotsMonitoringImagePath")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("TEXT");

                    b.Property<string>("ScreenShotsMonitoringStatus")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("TaskId")
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("OrganizationId");

                    b.HasIndex("TaskId");

                    b.ToTable("ScreenShotsMonitoring");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ScreensAccessProfileDetailsModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<bool>("CanAdd")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("CanDelete")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("CanEdit")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("CanOpen")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("CanPrint")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("CanShow")
                        .HasColumnType("INTEGER");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("ScreenId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("ScreensAccessProfileId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId");

                    b.HasIndex("ScreensAccessProfileId");

                    b.ToTable("ScreensAccessProfileDetails");

                    b.HasData(
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 69, 87, 65 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 113, 91, 23, 139, 210, 90, 62, 161, 207, 211, 212, 19 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 70, 48, 54 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 128, 206, 111, 152, 245, 207, 203, 185, 163, 37, 37, 96 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 70, 51, 70 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 141, 251, 218, 221, 101, 124, 150, 52, 184, 149, 31, 122 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 70, 55, 48 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 156, 1, 249, 101, 170, 105, 0, 49, 130, 133, 141, 224 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 70, 65, 87 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 171, 148, 227, 201, 202, 4, 52, 219, 242, 76, 56, 167 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 70, 69, 48 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 184, 21, 230, 189, 241, 16, 10, 102, 233, 215, 81, 175 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 70, 72, 69 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 197, 203, 229, 12, 198, 162, 249, 155, 126, 253, 76, 92 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 70, 78, 90 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 215, 229, 114, 67, 250, 106, 105, 30, 118, 191, 158, 196 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 70, 82, 89 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 227, 200, 190, 9, 108, 116, 65, 203, 19, 33, 15, 144 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 70, 87, 55 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 240, 228, 236, 178, 118, 9, 166, 1, 117, 86, 52, 70 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 69, 82, 90, 54, 87 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 221, 143, 155, 151, 174, 201, 71, 197, 199, 34, 127, 130, 213, 20 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 69, 82, 90, 66, 71 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 221, 143, 174, 28, 81, 29, 12, 96, 160, 164, 74, 111, 236, 232 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 70, 90, 49 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 252, 62, 89, 175, 141, 219, 54, 183, 167, 93, 97, 243 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 71, 52, 54 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 16, 212, 246, 29, 246, 9, 248, 50, 11, 157, 200, 23 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 71, 55, 50 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 28, 65, 137, 58, 51, 220, 12, 156, 155, 205, 249, 180 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 71, 57, 74 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 38, 94, 105, 38, 216, 183, 11, 177, 132, 35, 76, 102 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 71, 68, 75 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 54, 113, 69, 64, 222, 18, 219, 159, 8, 98, 1, 96 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 71, 71, 81 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 66, 231, 214, 133, 78, 75, 118, 239, 189, 84, 107, 76 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 71, 75, 82 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 79, 28, 143, 88, 190, 61, 68, 108, 17, 81, 108, 104 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 72, 54, 56 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 153, 5, 82, 189, 28, 115, 169, 39, 235, 141, 38, 235 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 72, 65, 87 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 171, 147, 25, 95, 76, 149, 24, 162, 55, 190, 115, 191 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 72, 68, 67 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 181, 143, 245, 31, 25, 38, 105, 242, 182, 103, 160, 63 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 72, 72, 88 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 199, 179, 25, 221, 252, 247, 93, 218, 29, 212, 45, 229 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 72, 77, 71 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 210, 19, 8, 221, 6, 29, 248, 241, 174, 65, 105, 12 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 72, 81, 51 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 220, 109, 25, 93, 57, 241, 79, 127, 208, 95, 164, 247 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 72, 84, 54 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 232, 194, 3, 188, 40, 41, 38, 77, 10, 43, 43, 175 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 72, 88, 81 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 246, 234, 154, 76, 3, 20, 174, 106, 140, 228, 86, 114 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 74, 49, 50 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 217, 4, 93, 0, 221, 108, 243, 248, 38, 229, 6, 139, 170 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 74, 52, 82 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 217, 19, 23, 23, 21, 156, 127, 142, 224, 59, 54, 193, 31 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 74, 56, 89 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 217, 35, 212, 255, 43, 144, 147, 221, 122, 140, 253, 106, 140 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 74, 66, 83 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 217, 47, 62, 22, 123, 215, 67, 81, 202, 247, 93, 43, 244 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 74, 70, 49 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 217, 60, 50, 204, 74, 208, 189, 103, 148, 108, 175, 12, 226 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 69, 87, 65 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 113, 91, 23, 139, 210, 90, 62, 161, 207, 211, 212, 19 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 70, 48, 54 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 128, 206, 111, 152, 245, 207, 203, 185, 163, 37, 37, 96 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 70, 51, 70 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 141, 251, 218, 221, 101, 124, 150, 52, 184, 149, 31, 122 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 70, 55, 48 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 156, 1, 249, 101, 170, 105, 0, 49, 130, 133, 141, 224 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 70, 65, 87 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 171, 148, 227, 201, 202, 4, 52, 219, 242, 76, 56, 167 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 70, 69, 48 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 184, 21, 230, 189, 241, 16, 10, 102, 233, 215, 81, 175 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 70, 72, 69 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 197, 203, 229, 12, 198, 162, 249, 155, 126, 253, 76, 92 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 70, 78, 90 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 215, 229, 114, 67, 250, 106, 105, 30, 118, 191, 158, 196 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 70, 82, 89 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 227, 200, 190, 9, 108, 116, 65, 203, 19, 33, 15, 144 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 70, 87, 55 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 240, 228, 236, 178, 118, 9, 166, 1, 117, 86, 52, 70 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 69, 82, 90, 54, 87 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 221, 143, 155, 151, 174, 201, 71, 197, 199, 34, 127, 130, 213, 20 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 69, 82, 90, 66, 71 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 221, 143, 174, 28, 81, 29, 12, 96, 160, 164, 74, 111, 236, 232 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 70, 90, 49 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 215, 252, 62, 89, 175, 141, 219, 54, 183, 167, 93, 97, 243 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 71, 52, 54 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 16, 212, 246, 29, 246, 9, 248, 50, 11, 157, 200, 23 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 71, 55, 50 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 28, 65, 137, 58, 51, 220, 12, 156, 155, 205, 249, 180 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 71, 57, 74 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 38, 94, 105, 38, 216, 183, 11, 177, 132, 35, 76, 102 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 71, 68, 75 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 54, 113, 69, 64, 222, 18, 219, 159, 8, 98, 1, 96 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 71, 71, 81 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 66, 231, 214, 133, 78, 75, 118, 239, 189, 84, 107, 76 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 71, 75, 82 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 79, 28, 143, 88, 190, 61, 68, 108, 17, 81, 108, 104 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 72, 54, 56 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 153, 5, 82, 189, 28, 115, 169, 39, 235, 141, 38, 235 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 72, 65, 87 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 171, 147, 25, 95, 76, 149, 24, 162, 55, 190, 115, 191 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 72, 68, 67 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 181, 143, 245, 31, 25, 38, 105, 242, 182, 103, 160, 63 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 72, 72, 88 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 199, 179, 25, 221, 252, 247, 93, 218, 29, 212, 45, 229 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 72, 77, 71 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 210, 19, 8, 221, 6, 29, 248, 241, 174, 65, 105, 12 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 72, 81, 51 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 220, 109, 25, 93, 57, 241, 79, 127, 208, 95, 164, 247 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 72, 84, 54 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 232, 194, 3, 188, 40, 41, 38, 77, 10, 43, 43, 175 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 72, 88, 81 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 216, 246, 234, 154, 76, 3, 20, 174, 106, 140, 228, 86, 114 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 74, 49, 50 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 217, 4, 93, 0, 221, 108, 243, 248, 38, 229, 6, 139, 170 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 74, 52, 82 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 217, 19, 23, 23, 21, 156, 127, 142, 224, 59, 54, 193, 31 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 74, 56, 89 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 217, 35, 212, 255, 43, 144, 147, 221, 122, 140, 253, 106, 140 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 74, 66, 83 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 217, 47, 62, 22, 123, 215, 67, 81, 202, 247, 93, 43, 244 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 74, 70, 49 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenId = new byte[] { 1, 150, 218, 217, 60, 50, 204, 74, 208, 189, 103, 148, 108, 175, 12, 226 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        });
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ScreensAccessProfileModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<string>("ScreensAccessProfileName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId");

                    b.ToTable("ScreensAccessProfile");

                    b.HasData(
                        new
                        {
                            Id = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 },
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreensAccessProfileName = "Admin Profile"
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 },
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreensAccessProfileName = "User Profile"
                        });
                });

            modelBuilder.Entity("LeadTeams.Models.Model.SettingModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<int>("IdlePopUpMaxWaitTime")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<int>("MaxIdleTime")
                        .HasColumnType("INTEGER");

                    b.Property<int>("MaxRandomScreenShot")
                        .HasColumnType("INTEGER");

                    b.Property<int>("MinRandomScreenShot")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<int>("ScreenShotInterval")
                        .HasColumnType("INTEGER");

                    b.Property<int>("StopPopUpMaxWaitTime")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId")
                        .IsUnique();

                    b.ToTable("Setting");

                    b.HasData(
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 98, 196, 76, 123, 89, 234, 170, 239, 176, 79, 16 },
                            IdlePopUpMaxWaitTime = 10,
                            IsActive = true,
                            MaxIdleTime = 180,
                            MaxRandomScreenShot = 300,
                            MinRandomScreenShot = 60,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ScreenShotInterval = 300,
                            StopPopUpMaxWaitTime = 10
                        });
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ShiftDynamicPatternModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<int>("DayOfWeek")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("ShiftId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<TimeSpan>("TargetTime")
                        .HasColumnType("Time");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId");

                    b.HasIndex("ShiftId");

                    b.ToTable("ShiftDynamicPattern");

                    b.HasData(
                        new
                        {
                            Id = new byte[] { 1, 149, 113, 243, 207, 83, 180, 121, 149, 38, 127, 42, 170, 197, 134, 164 },
                            DayOfWeek = 1,
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 44, 62, 114, 205, 124, 191, 187, 231, 209, 195 },
                            TargetTime = new TimeSpan(0, 8, 0, 0, 0)
                        },
                        new
                        {
                            Id = new byte[] { 1, 149, 113, 244, 18, 224, 67, 68, 71, 203, 49, 251, 98, 94, 212, 187 },
                            DayOfWeek = 2,
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 44, 62, 114, 205, 124, 191, 187, 231, 209, 195 },
                            TargetTime = new TimeSpan(0, 8, 0, 0, 0)
                        },
                        new
                        {
                            Id = new byte[] { 1, 149, 113, 244, 35, 166, 107, 177, 49, 3, 99, 90, 147, 202, 71, 135 },
                            DayOfWeek = 3,
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 44, 62, 114, 205, 124, 191, 187, 231, 209, 195 },
                            TargetTime = new TimeSpan(0, 8, 0, 0, 0)
                        },
                        new
                        {
                            Id = new byte[] { 1, 149, 113, 244, 45, 218, 158, 171, 191, 66, 26, 54, 80, 60, 4, 158 },
                            DayOfWeek = 4,
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 44, 62, 114, 205, 124, 191, 187, 231, 209, 195 },
                            TargetTime = new TimeSpan(0, 8, 0, 0, 0)
                        },
                        new
                        {
                            Id = new byte[] { 1, 149, 113, 244, 57, 83, 224, 21, 251, 163, 43, 143, 47, 88, 241, 19 },
                            DayOfWeek = 5,
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 44, 62, 114, 205, 124, 191, 187, 231, 209, 195 },
                            TargetTime = new TimeSpan(0, 8, 0, 0, 0)
                        });
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ShiftFixedPatternModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<TimeSpan>("ArrivalTime")
                        .HasColumnType("Time");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<int>("DayOfWeek")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<TimeSpan>("DepartureTime")
                        .HasColumnType("Time");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("ShiftId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<int>("ShiftNumber")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId");

                    b.HasIndex("ShiftId");

                    b.ToTable("ShiftFixedPattern");

                    b.HasData(
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 98, 73, 103, 97, 75, 117, 122, 48, 225, 9, 229 },
                            ArrivalTime = new TimeSpan(0, 8, 0, 0, 0),
                            DayOfWeek = 1,
                            DepartureTime = new TimeSpan(0, 16, 0, 0, 0),
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 249, 194, 128, 176, 2, 72, 168, 14, 186, 109 },
                            ShiftNumber = 1
                        },
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 99, 171, 56, 163, 94, 125, 84, 106, 221, 234, 29 },
                            ArrivalTime = new TimeSpan(0, 8, 0, 0, 0),
                            DayOfWeek = 2,
                            DepartureTime = new TimeSpan(0, 16, 0, 0, 0),
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 249, 194, 128, 176, 2, 72, 168, 14, 186, 109 },
                            ShiftNumber = 1
                        },
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 99, 27, 143, 56, 37, 162, 184, 38, 10, 88, 207 },
                            ArrivalTime = new TimeSpan(0, 8, 0, 0, 0),
                            DayOfWeek = 3,
                            DepartureTime = new TimeSpan(0, 16, 0, 0, 0),
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 249, 194, 128, 176, 2, 72, 168, 14, 186, 109 },
                            ShiftNumber = 1
                        },
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 99, 3, 117, 234, 59, 136, 218, 33, 201, 50, 118 },
                            ArrivalTime = new TimeSpan(0, 8, 0, 0, 0),
                            DayOfWeek = 4,
                            DepartureTime = new TimeSpan(0, 16, 0, 0, 0),
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 249, 194, 128, 176, 2, 72, 168, 14, 186, 109 },
                            ShiftNumber = 1
                        },
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 99, 227, 128, 22, 166, 247, 105, 226, 33, 25, 87 },
                            ArrivalTime = new TimeSpan(0, 8, 0, 0, 0),
                            DayOfWeek = 5,
                            DepartureTime = new TimeSpan(0, 16, 0, 0, 0),
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 249, 194, 128, 176, 2, 72, 168, 14, 186, 109 },
                            ShiftNumber = 1
                        },
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 99, 82, 114, 66, 76, 166, 230, 111, 167, 20, 69 },
                            ArrivalTime = new TimeSpan(0, 8, 0, 0, 0),
                            DayOfWeek = 6,
                            DepartureTime = new TimeSpan(0, 16, 0, 0, 0),
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 249, 194, 128, 176, 2, 72, 168, 14, 186, 109 },
                            ShiftNumber = 1
                        },
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 99, 100, 197, 68, 167, 143, 238, 78, 180, 139, 174 },
                            ArrivalTime = new TimeSpan(0, 8, 0, 0, 0),
                            DayOfWeek = 7,
                            DepartureTime = new TimeSpan(0, 16, 0, 0, 0),
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 249, 194, 128, 176, 2, 72, 168, 14, 186, 109 },
                            ShiftNumber = 1
                        },
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 99, 46, 106, 119, 71, 181, 169, 200, 63, 167, 156 },
                            ArrivalTime = new TimeSpan(0, 8, 0, 0, 0),
                            DayOfWeek = 1,
                            DepartureTime = new TimeSpan(0, 16, 0, 0, 0),
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 249, 194, 128, 176, 2, 72, 168, 14, 186, 109 },
                            ShiftNumber = 2
                        },
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 99, 48, 86, 255, 58, 229, 66, 91, 97, 152, 142 },
                            ArrivalTime = new TimeSpan(0, 8, 0, 0, 0),
                            DayOfWeek = 2,
                            DepartureTime = new TimeSpan(0, 16, 0, 0, 0),
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 249, 194, 128, 176, 2, 72, 168, 14, 186, 109 },
                            ShiftNumber = 2
                        },
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 99, 176, 118, 220, 164, 244, 13, 184, 7, 170, 4 },
                            ArrivalTime = new TimeSpan(0, 8, 0, 0, 0),
                            DayOfWeek = 3,
                            DepartureTime = new TimeSpan(0, 16, 0, 0, 0),
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 249, 194, 128, 176, 2, 72, 168, 14, 186, 109 },
                            ShiftNumber = 2
                        },
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 99, 166, 184, 247, 204, 4, 231, 234, 29, 88, 50 },
                            ArrivalTime = new TimeSpan(0, 8, 0, 0, 0),
                            DayOfWeek = 4,
                            DepartureTime = new TimeSpan(0, 16, 0, 0, 0),
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 249, 194, 128, 176, 2, 72, 168, 14, 186, 109 },
                            ShiftNumber = 2
                        },
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 100, 123, 109, 8, 245, 235, 115, 62, 151, 171, 44 },
                            ArrivalTime = new TimeSpan(0, 8, 0, 0, 0),
                            DayOfWeek = 5,
                            DepartureTime = new TimeSpan(0, 16, 0, 0, 0),
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 249, 194, 128, 176, 2, 72, 168, 14, 186, 109 },
                            ShiftNumber = 2
                        },
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 100, 141, 7, 73, 167, 129, 16, 68, 150, 41, 88 },
                            ArrivalTime = new TimeSpan(0, 8, 0, 0, 0),
                            DayOfWeek = 6,
                            DepartureTime = new TimeSpan(0, 16, 0, 0, 0),
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 249, 194, 128, 176, 2, 72, 168, 14, 186, 109 },
                            ShiftNumber = 2
                        },
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 100, 79, 109, 240, 172, 137, 237, 231, 92, 7, 45 },
                            ArrivalTime = new TimeSpan(0, 8, 0, 0, 0),
                            DayOfWeek = 7,
                            DepartureTime = new TimeSpan(0, 16, 0, 0, 0),
                            IsActive = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftId = new byte[] { 1, 149, 16, 148, 117, 98, 249, 194, 128, 176, 2, 72, 168, 14, 186, 109 },
                            ShiftNumber = 2
                        });
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ShiftModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsFixedPattern")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<string>("ShiftName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId");

                    b.ToTable("Shift");

                    b.HasData(
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 98, 249, 194, 128, 176, 2, 72, 168, 14, 186, 109 },
                            IsActive = true,
                            IsFixedPattern = true,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftName = "Demo Main Fixed Shift"
                        },
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 98, 44, 62, 114, 205, 124, 191, 187, 231, 209, 195 },
                            IsActive = true,
                            IsFixedPattern = false,
                            OrganizationId = new byte[] { 1, 149, 16, 148, 117, 97, 17, 226, 248, 174, 75, 149, 227, 94, 125, 141 },
                            ShiftName = "Demo Main Target Shift"
                        });
                });

            modelBuilder.Entity("LeadTeams.Models.Model.TaskModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("EmployeeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("OrganizationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("ProjectId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<DateTime>("TaskAssignDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("TaskDeadLineDate")
                        .HasColumnType("datetime");

                    b.Property<string>("TaskDescription")
                        .HasColumnType("TEXT");

                    b.Property<string>("TaskName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("TaskPriority")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT");

                    b.Property<string>("TaskStatus")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("OrganizationId");

                    b.HasIndex("ProjectId");

                    b.ToTable("Task");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.AllowanceModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("Allowances")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.AskLeaveModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.EmployeeModel", "AskLeaveRequestedBy")
                        .WithMany("AskLeavesRequest")
                        .HasForeignKey("AskLeaveRequestedById")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.EmployeeModel", "AskLeaveStatusBy")
                        .WithMany("AskLeavesStatusBy")
                        .HasForeignKey("AskLeaveStatusById");

                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("AskLeaves")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("AskLeaveRequestedBy");

                    b.Navigation("AskLeaveStatusBy");

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.AttendanceLogModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.EmployeeModel", "Employee")
                        .WithMany("AttendanceLogs")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("AttendanceLogs")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.TaskModel", "Task")
                        .WithMany("AttendanceLogs")
                        .HasForeignKey("TaskId");

                    b.Navigation("Employee");

                    b.Navigation("Organization");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.AuditModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.EmployeeModel", "Employee")
                        .WithMany("Audits")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("Audits")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Employee");

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.EmployeeAllowanceModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.AllowanceModel", "Allowance")
                        .WithMany("EmployeeAllowances")
                        .HasForeignKey("AllowanceId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.EmployeeModel", "Employee")
                        .WithMany("EmployeeAllowances")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("EmployeeAllowances")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Allowance");

                    b.Navigation("Employee");

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.EmployeeEducationalQualificationModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.EmployeeModel", "Employee")
                        .WithMany("EmployeeEducationalQualifications")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("EmployeeEducationalQualifications")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Employee");

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.EmployeeKidsModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.EmployeeModel", "Employee")
                        .WithMany("EmployeeKids")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("EmployeeKids")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Employee");

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.EmployeeManagerModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.EmployeeModel", "Employee")
                        .WithMany("Employees")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.EmployeeModel", "Manager")
                        .WithMany("Managers")
                        .HasForeignKey("ManagerId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("EmployeeManagers")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Employee");

                    b.Navigation("Manager");

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.EmployeeModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("Employees")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.ScreensAccessProfileModel", "ScreensAccessProfile")
                        .WithMany("Users")
                        .HasForeignKey("ScreensAccessProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.ShiftModel", "Shift")
                        .WithMany("Employees")
                        .HasForeignKey("ShiftId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Organization");

                    b.Navigation("ScreensAccessProfile");

                    b.Navigation("Shift");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.LoginSessionModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.EmployeeModel", "Employee")
                        .WithMany("LoginSessions")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("LoginSessions")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Employee");

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ManagementTeamEmployeeModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.EmployeeModel", "Employee")
                        .WithMany("ManagementTeamEmployees")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.ManagementTeamModel", "ManagementTeam")
                        .WithMany("ManagementTeamEmployees")
                        .HasForeignKey("ManagementTeamId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("ManagementTeamEmployees")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Employee");

                    b.Navigation("ManagementTeam");

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ManagementTeamManagerModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.ManagementTeamModel", "ManagementTeam")
                        .WithMany("ManagementTeamManagers")
                        .HasForeignKey("ManagementTeamId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.EmployeeModel", "Manager")
                        .WithMany("ManagementTeamManagers")
                        .HasForeignKey("ManagerId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("ManagementTeamManagers")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("ManagementTeam");

                    b.Navigation("Manager");

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ManagementTeamModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("ManagementTeams")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.MeetingEmployeeModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.EmployeeModel", "Employee")
                        .WithMany("MeetingEmployees")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.MeetingModel", "Meeting")
                        .WithMany("MeetingEmployees")
                        .HasForeignKey("MeetingId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("MeetingEmployees")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Employee");

                    b.Navigation("Meeting");

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.MeetingModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("Meetings")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.ProjectModel", "Project")
                        .WithMany("Meetings")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("Organization");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.MessageModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.EmployeeModel", "EmployeeFrom")
                        .WithMany("MessagesFrom")
                        .HasForeignKey("EmployeeFromId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.EmployeeModel", "Employee")
                        .WithMany("Messages")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.EmployeeModel", "EmployeeTo")
                        .WithMany("MessagesTo")
                        .HasForeignKey("EmployeeToId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("Messages")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Employee");

                    b.Navigation("EmployeeFrom");

                    b.Navigation("EmployeeTo");

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.OrganizationNewsModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("OrganizationNews")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ProjectModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("Projects")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.RefreshTokenModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany()
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.EmployeeModel", "User")
                        .WithMany("RefreshTokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Organization");

                    b.Navigation("User");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ScreenShotsMonitoringModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.EmployeeModel", "Employee")
                        .WithMany("ScreenShotsMonitorings")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("ScreenShotsMonitorings")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.TaskModel", "Task")
                        .WithMany("ScreenShotsMonitorings")
                        .HasForeignKey("TaskId");

                    b.Navigation("Employee");

                    b.Navigation("Organization");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ScreensAccessProfileDetailsModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("ScreensAccessProfileDetails")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.ScreensAccessProfileModel", "ScreensAccessProfile")
                        .WithMany("ScreensAccessProfileDetails")
                        .HasForeignKey("ScreensAccessProfileId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Organization");

                    b.Navigation("ScreensAccessProfile");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ScreensAccessProfileModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("ScreensAccessProfiles")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.SettingModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithOne("Setting")
                        .HasForeignKey("LeadTeams.Models.Model.SettingModel", "OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ShiftDynamicPatternModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("ShiftDynamicPatterns")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.ShiftModel", "Shift")
                        .WithMany("ShiftDynamicPatterns")
                        .HasForeignKey("ShiftId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Organization");

                    b.Navigation("Shift");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ShiftFixedPatternModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("ShiftFixedPatterns")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.ShiftModel", "Shift")
                        .WithMany("ShiftFixedPatterns")
                        .HasForeignKey("ShiftId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Organization");

                    b.Navigation("Shift");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ShiftModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("Shifts")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.TaskModel", b =>
                {
                    b.HasOne("LeadTeams.Models.Model.EmployeeModel", "Employee")
                        .WithMany("Tasks")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.OrganizationModel", "Organization")
                        .WithMany("Tasks")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("LeadTeams.Models.Model.ProjectModel", "Project")
                        .WithMany("Tasks")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Employee");

                    b.Navigation("Organization");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.AllowanceModel", b =>
                {
                    b.Navigation("EmployeeAllowances");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.EmployeeModel", b =>
                {
                    b.Navigation("AskLeavesRequest");

                    b.Navigation("AskLeavesStatusBy");

                    b.Navigation("AttendanceLogs");

                    b.Navigation("Audits");

                    b.Navigation("EmployeeAllowances");

                    b.Navigation("EmployeeEducationalQualifications");

                    b.Navigation("EmployeeKids");

                    b.Navigation("Employees");

                    b.Navigation("LoginSessions");

                    b.Navigation("ManagementTeamEmployees");

                    b.Navigation("ManagementTeamManagers");

                    b.Navigation("Managers");

                    b.Navigation("MeetingEmployees");

                    b.Navigation("Messages");

                    b.Navigation("MessagesFrom");

                    b.Navigation("MessagesTo");

                    b.Navigation("RefreshTokens");

                    b.Navigation("ScreenShotsMonitorings");

                    b.Navigation("Tasks");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ManagementTeamModel", b =>
                {
                    b.Navigation("ManagementTeamEmployees");

                    b.Navigation("ManagementTeamManagers");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.MeetingModel", b =>
                {
                    b.Navigation("MeetingEmployees");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.OrganizationModel", b =>
                {
                    b.Navigation("Allowances");

                    b.Navigation("AskLeaves");

                    b.Navigation("AttendanceLogs");

                    b.Navigation("Audits");

                    b.Navigation("EmployeeAllowances");

                    b.Navigation("EmployeeEducationalQualifications");

                    b.Navigation("EmployeeKids");

                    b.Navigation("EmployeeManagers");

                    b.Navigation("Employees");

                    b.Navigation("LoginSessions");

                    b.Navigation("ManagementTeamEmployees");

                    b.Navigation("ManagementTeamManagers");

                    b.Navigation("ManagementTeams");

                    b.Navigation("MeetingEmployees");

                    b.Navigation("Meetings");

                    b.Navigation("Messages");

                    b.Navigation("OrganizationNews");

                    b.Navigation("Projects");

                    b.Navigation("ScreenShotsMonitorings");

                    b.Navigation("ScreensAccessProfileDetails");

                    b.Navigation("ScreensAccessProfiles");

                    b.Navigation("Setting")
                        .IsRequired();

                    b.Navigation("ShiftDynamicPatterns");

                    b.Navigation("ShiftFixedPatterns");

                    b.Navigation("Shifts");

                    b.Navigation("Tasks");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ProjectModel", b =>
                {
                    b.Navigation("Meetings");

                    b.Navigation("Tasks");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ScreensAccessProfileModel", b =>
                {
                    b.Navigation("ScreensAccessProfileDetails");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.ShiftModel", b =>
                {
                    b.Navigation("Employees");

                    b.Navigation("ShiftDynamicPatterns");

                    b.Navigation("ShiftFixedPatterns");
                });

            modelBuilder.Entity("LeadTeams.Models.Model.TaskModel", b =>
                {
                    b.Navigation("AttendanceLogs");

                    b.Navigation("ScreenShotsMonitorings");
                });
#pragma warning restore 612, 618
        }
    }
}
