﻿namespace GMCadiomCore.Presenters.BasePresenter
{
    internal interface IBaseReportPresenter<T1> : IBaseReportPresenter where T1 : class
    {
        IBaseReport<T1> View { get; }
        BindingList<T1> MainList { get; }
        T1 CurrentModel { get; }
    }

    internal interface IBaseReportPresenter
    {
        IBaseUnitOfWork Repository { get; }
        BindingSource MainBindingSource { get; }
        void LoadAllActionList();
        void ExportAction();
        void SearchAction();
    }
}
