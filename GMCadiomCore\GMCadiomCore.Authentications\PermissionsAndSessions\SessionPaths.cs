﻿namespace GMCadiomCore.Authentications.PermissionsAndSessions
{
    internal class SessionPath
    {
        internal string Name { get; set; } = "GMCadiomSession";

        internal string SessionsBasePath()
        {
            //C:\Users\<USER>\AppData
            string Location = Path.GetDirectoryName(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData)) ?? string.Empty;

            string currentDirectoryPath = Path.Combine(Location, Name);
            if (!Directory.Exists(currentDirectoryPath))
                Directory.CreateDirectory(currentDirectoryPath);

            DirectoryInfo FileLocation = new DirectoryInfo(currentDirectoryPath);

            return FileLocation.FullName;
        }

        internal DirectoryInfo CreateDirectory(string folder, string? path = null)
        {
            string directoryPath = Path.Combine(path ?? SessionsBasePath(), folder);
            if (!Directory.Exists(directoryPath))
                Directory.CreateDirectory(directoryPath);

            DirectoryInfo directoryInfo = new DirectoryInfo(directoryPath);
            return directoryInfo;
        }
    }

    public static class SessionPaths
    {
        private static SessionPath _sessionPath;
        private static SessionPath sessionPath => _sessionPath ??= new SessionPath();

        public static void SetName(string name) => sessionPath.Name = name;

        public static string SessionsBasePath() => sessionPath.SessionsBasePath();

        public static string SessionsTempListPath() => sessionPath.CreateDirectory("TempList").FullName;

        public static string SessionUserDataPath() => sessionPath.CreateDirectory("UserData").FullName;

        public static string SessionCachePath() => sessionPath.CreateDirectory("Cache").FullName;

        public static string SessionLogsPath() => sessionPath.CreateDirectory("Logs", SessionCachePath()).FullName;

        public static string LoggerPath() => Path.Combine(SessionLogsPath(), $"Log.txt");

        public static string SessionConfigurationsPath() => sessionPath.CreateDirectory("Configurations", SessionUserDataPath()).FullName;

        public static string SessionDataGridViewConfigurationsPath() => sessionPath.CreateDirectory("DataGridView", SessionConfigurationsPath()).FullName;
    }
}
