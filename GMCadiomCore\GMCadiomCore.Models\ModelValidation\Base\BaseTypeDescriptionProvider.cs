﻿namespace GMCadiomCore.Models.ModelValidation.Base
{
    public class BaseTypeDescriptionProvider<T> : TypeDescriptionProvider
    {
        private static TypeDescriptionProvider defaultTypeProvider = TypeDescriptor.GetProvider(typeof(T));

        public BaseTypeDescriptionProvider() : base(defaultTypeProvider) { }

        public override ICustomTypeDescriptor? GetTypeDescriptor(Type objectType, object? instance)
        {
            if (instance == null)
                instance = Activator.CreateInstance(typeof(T));

            ICustomTypeDescriptor? defaultDescriptor = null;
            try
            {
                defaultDescriptor = base.GetTypeDescriptor(objectType, instance);
            }
            catch
            {
                return GetTypeDescriptor(defaultTypeProvider);
            }
            return instance == null ? defaultDescriptor : new BaseCustomTypeDescriptor(defaultDescriptor, instance);
        }
    }
}
