﻿namespace GMCadiomCore.Repositories.API.Factory
{
    public class BaseRepository<TEntity, TEntityView> : IBaseRepository<TEntity, TEntityView> where TEntity : BaseIdentityModel where TEntityView : BaseIdentityModel
    {
        private HttpClient client;
        private string baseAddress;

        public string GetPath { get; private set; }
        public string GetByPath { get; private set; }
        public string CreatePath { get; private set; }
        public string UpdatePath { get; private set; }
        public string DeletePath { get; private set; }

        public BaseRepository(string baseAddress, string controllerPath, string getPath, string getByPath, string createPath, string updatePath, string deletePath)
        {
            this.baseAddress = baseAddress;
            GetPath = SetFullPath(controllerPath, getPath);
            GetByPath = SetFullPath(controllerPath, getByPath);
            CreatePath = SetFullPath(controllerPath, createPath);
            UpdatePath = SetFullPath(controllerPath, updatePath);
            DeletePath = SetFullPath(controllerPath, deletePath);
        }

        #region Async
        public async Task<TEntity?> AddAsync(TEntity? entity)
        {
            if (entity != null)
            {
                client = new HttpClient();
                HttpRequestMessage? request = CreateRequest(HttpMethod.Post, CreatePath, entity);
                if (request != null)
                {
                    HttpResponseMessage response = await client.SendAsync(request);
                    if (response.IsSuccessStatusCode)
                    {
                        TEntity? result = await response.Content.ReadFromJsonAsync<TEntity>();
                        if (result != null)
                        {
                            DataBaseWatcher.NotifyDataBaseWatcherChanged(new DataBaseEntity()
                            {
                                Entity = result,
                                EntityState = 4,
                                EntityType = typeof(TEntity).Name,
                            });
                        }
                        return result;
                    }
                    throw new HttpRequestException($"{response.StatusCode} - {response.ReasonPhrase}");
                }
                throw new HttpRequestException($"There a problem creating the request.");
            }
            return null;
        }

        public async Task<TEntity?> UpdateAsync(TEntity? entity)
        {
            if (entity != null)
            {
                client = new HttpClient();
                HttpRequestMessage? request = CreateRequest(HttpMethod.Post, UpdatePath, entity);
                if (request != null)
                {
                    HttpResponseMessage response = await client.SendAsync(request);
                    if (response.IsSuccessStatusCode)
                    {
                        TEntity? result = await response.Content.ReadFromJsonAsync<TEntity>();
                        if (result != null)
                        {
                            DataBaseWatcher.NotifyDataBaseWatcherChanged(new DataBaseEntity()
                            {
                                Entity = result,
                                EntityState = 4,
                                EntityType = typeof(TEntity).Name,
                            });
                        }
                        return result;
                    }
                    throw new HttpRequestException($"{response.StatusCode} - {response.ReasonPhrase}");
                }
                throw new HttpRequestException($"There a problem creating the request.");
            }
            return null;
        }

        public async Task<TEntity?> RemoveAsync(TEntity? entity)
        {
            if (entity != null)
            {
                client = new HttpClient();
                HttpRequestMessage? request = CreateRequest(HttpMethod.Post, DeletePath, entity);
                if (request != null)
                {
                    HttpResponseMessage response = await client.SendAsync(request);
                    if (response.IsSuccessStatusCode)
                    {
                        TEntity? result = await response.Content.ReadFromJsonAsync<TEntity>();
                        if (result != null)
                        {
                            DataBaseWatcher.NotifyDataBaseWatcherChanged(new DataBaseEntity()
                            {
                                Entity = result,
                                EntityState = 4,
                                EntityType = typeof(TEntity).Name,
                            });
                        }
                        return result;
                    }
                    throw new HttpRequestException($"{response.StatusCode} - {response.ReasonPhrase}");
                }
                throw new HttpRequestException($"There a problem creating the request.");
            }
            return null;
        }

        public async Task<TEntity?> GetAsync(RepositorySpecifications<TEntity>? repositorySpecifications = null)
        {
            client = new HttpClient();
            HttpRequestMessage? request = CreateRequest(HttpMethod.Get, GetByPath /*+ By*/, null /*By*/);
            if (request != null)
            {
                HttpResponseMessage response = await client.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    TEntity? result = await response.Content.ReadFromJsonAsync<TEntity>();
                    return result;
                }
            }
            return null;
        }

        public Task<bool> IsExistAsync(Ulid id)
        {
            throw new NotImplementedException();
        }

        public async Task<PaginationList<TEntity>> GetAllPaginationListAsync(PaginationSpecifications<TEntity> paginationSpecifications)
        {
            client = new HttpClient();
            HttpRequestMessage? request = CreateRequest(HttpMethod.Get, GetPath, null);
            if (request != null)
            {
                if (request != null)
                {
                    HttpResponseMessage response = await client.SendAsync(request);
                    if (response.IsSuccessStatusCode)
                    {
                        IEnumerable<TEntity>? query = await response.Content.ReadFromJsonAsync<IEnumerable<TEntity>>();
                        if (query != null)
                        {
                            if (paginationSpecifications.SearchValue != null)
                                query = query.AsQueryable().Where(paginationSpecifications.SearchValue).ToList();
                            int totalCount = query.Count();
                            query = query.Skip((paginationSpecifications.PageNumber - 1) * paginationSpecifications.PageSize).Take(paginationSpecifications.PageSize);
                            var entities = query.ToList();
                            return new PaginationList<TEntity>(entities, totalCount, paginationSpecifications.PageNumber, paginationSpecifications.PageSize);
                        }
                    }
                }
            }
            throw new HttpRequestException($"There a problem creating the request.");
        }

        public async Task<List<TEntity>> GetAllAsync(RepositorySpecifications<TEntity>? repositorySpecifications = null)
        {
            client = new HttpClient();
            HttpRequestMessage? request = CreateRequest(HttpMethod.Get, GetPath, null);
            if (request != null)
            {
                if (request != null)
                {
                    HttpResponseMessage response = await client.SendAsync(request);
                    if (response.IsSuccessStatusCode)
                    {
                        List<TEntity>? entities = await response.Content.ReadFromJsonAsync<List<TEntity>>();
                        if (entities != null)
                        {
                            if (repositorySpecifications != null)
                                if (repositorySpecifications.SearchValue != null)
                                    entities = entities.AsQueryable().Where(repositorySpecifications.SearchValue).ToList();
                            return entities;
                        }
                    }
                }
            }
            throw new HttpRequestException($"There a problem creating the request.");
        }

        public Task<PaginationList<TEntity>> GetAllByNameAsync(string name, PaginationSpecifications<TEntity> paginationSpecifications)
        {
            throw new NotImplementedException();
        }

        public Task<List<IdAndName>> GetAsSelectedItemsAsync(RepositorySpecifications<TEntity>? repositorySpecifications = null)
        {
            throw new NotImplementedException();
        }

        public Task<int> GetMaxIdAsync(RepositorySpecifications<TEntity>? repositorySpecifications = null)
        {
            throw new NotImplementedException();
        }

        public virtual Task<PaginationList<TEntityView>> GetAllViewAsync(PaginationSpecifications<TEntityView> paginationSpecifications)
        {
            throw new NotImplementedException();
        }
        #endregion

        #region Sync
        public TEntity? Add(TEntity? entity) => Task.Run(async () => await AddAsync(entity)).GetAwaiter().GetResult();

        public TEntity? Update(TEntity? entity) => Task.Run(async () => await UpdateAsync(entity)).GetAwaiter().GetResult();

        public TEntity? Remove(TEntity? entity) => Task.Run(async () => await RemoveAsync(entity)).GetAwaiter().GetResult();

        public TEntity? Get(RepositorySpecifications<TEntity>? repositorySpecifications = null) => Task.Run(async () => await GetAsync(repositorySpecifications)).GetAwaiter().GetResult();

        public bool IsExist(Ulid id) => Task.Run(async () => await IsExistAsync(id)).GetAwaiter().GetResult();

        public PaginationList<TEntity> GetAllPaginationList(PaginationSpecifications<TEntity> paginationSpecifications) => Task.Run(async () => await GetAllPaginationListAsync(paginationSpecifications)).GetAwaiter().GetResult();

        public List<TEntity> GetAll(RepositorySpecifications<TEntity>? repositorySpecifications = null) => Task.Run(async () => await GetAllAsync(repositorySpecifications)).GetAwaiter().GetResult();

        public PaginationList<TEntity> GetAllByName(string name, PaginationSpecifications<TEntity> paginationSpecifications) => Task.Run(async () => await GetAllByNameAsync(name, paginationSpecifications)).GetAwaiter().GetResult();

        public List<IdAndName> GetAsSelectedItems(RepositorySpecifications<TEntity>? repositorySpecifications = null) => Task.Run(async () => await GetAsSelectedItemsAsync(repositorySpecifications)).GetAwaiter().GetResult();

        public int GetMaxId(RepositorySpecifications<TEntity>? repositorySpecifications = null) => Task.Run(async () => await GetMaxIdAsync(repositorySpecifications)).GetAwaiter().GetResult();

        public PaginationList<TEntityView> GetAllView(PaginationSpecifications<TEntityView> paginationSpecifications) => Task.Run(async () => await GetAllViewAsync(paginationSpecifications)).GetAwaiter().GetResult();
        #endregion

        private HttpRequestMessage? CreateRequest(HttpMethod httpMethod, string path, object? content)
        {
            if (content == null || string.IsNullOrEmpty(path))
                return null;

            HttpRequestMessage request = new HttpRequestMessage(httpMethod, path);
            request.Headers.Accept.Clear();
            request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            //request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
            if (content != null)
            {
                string jsonContent = JsonConvert.SerializeObject(content, Formatting.Indented, new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    PreserveReferencesHandling = PreserveReferencesHandling.Objects,
                });
                request.Content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
            }

            return request;
        }

        private string SetFullPath(string controllerPath, string methodPath) => $"{baseAddress}/{controllerPath}/{methodPath}";
    }
}
