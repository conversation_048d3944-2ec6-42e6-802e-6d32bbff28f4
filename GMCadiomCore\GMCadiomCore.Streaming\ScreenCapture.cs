﻿namespace GMCadiomCore.Streaming
{
    public static class ScreenCapture
    {
        [DllImport("user32.dll")]
        private static extern nint GetForegroundWindow();

        [DllImport("user32.dll", CharSet = CharSet.Auto, ExactSpelling = true)]
        public static extern nint GetDesktopWindow();

        [StructLayout(LayoutKind.Sequential)]
        private struct Rect
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }

        [DllImport("user32.dll")]
        private static extern nint GetWindowRect(nint hWnd, ref Rect rect);

        public static Bitmap CaptureDesktop()
        {
            return CaptureWindow(GetDesktopWindow());
        }

        public static Bitmap CaptureActiveWindow()
        {
            return CaptureWindow(GetForegroundWindow());
        }

        public static Bitmap CaptureWindow(nint handle)
        {
            var rect = new Rect();
            GetWindowRect(handle, ref rect);
            Rectangle bounds = new Rectangle(rect.Left, rect.Top, rect.Right - rect.Left, rect.Bottom - rect.Top);
            Bitmap result = new Bitmap(bounds.Width, bounds.Height);

            using (Graphics graphics = Graphics.FromImage(result))
            {
                graphics.CopyFromScreen(new Point(bounds.Left, bounds.Top), Point.Empty, bounds.Size);
            }

            return result;
        }
    }

    public class ScreenShotEntity
    {
        public string ScreenShotName { get; set; }
        public DateTime ScreenShotDateTime { get; set; }
        public Bitmap ScreenShotBitmap { get; set; }
        public byte[] ScreenShotBitmapArray { get; set; }

        public ScreenShotEntity GerScreenShot(Bitmap bitmap)
        {
            return new ScreenShotEntity()
            {
                ScreenShotDateTime = DateTime.Now,
                ScreenShotBitmap = bitmap,
                ScreenShotBitmapArray = ImageToByteArray(bitmap),
            };
        }

        public override string ToString()
        {
            return $"{DateTime.Now.ToString("yyyy-MM-dd_hh-mm-ss tt")}";
        }

        private byte[] ImageToByteArray(Image image)
        {
            if (image != null)
            {
                using (MemoryStream memoryStream = new MemoryStream())
                {
                    new Bitmap(image).Save(memoryStream, ImageFormat.Jpeg);
                    return memoryStream.ToArray();
                }
            }
            return Array.Empty<byte>();
        }
    }
}
