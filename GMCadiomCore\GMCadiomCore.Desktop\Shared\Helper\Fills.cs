﻿namespace GMCadiomCore.Desktop.Shared.Helper
{
    public static class Fills
    {
        public static void FillComboBox<T>(this ComboBox cmb, Expression<Func<T, object>> displayMember, Expression<Func<T, object>> valueMember, object dataSource, bool AutoComplete = true)
        {
            cmb.DataSource = null;
            cmb.DisplayMember = GetCorrectPropertyName(displayMember);
            cmb.ValueMember = GetCorrectPropertyName(valueMember);
            cmb.DataSource = dataSource;
            cmb.SelectedIndex = -1;
            cmb.Format += (s, e) =>
            {
                if (e.Value != null)
                {
                    Type type = e.Value.GetType();
                    if (type.FullName == typeof(T).FullName)
                    {
                        //e.Value = ((T)e.Value).GetType()?.GetProperty(GetCorrectPropertyName(displayMember))?.GetValue(typeof(T), null);
                    }
                }
            };
            //TODO :
            //Fix System.Threading.ThreadStateException:
            //'Current thread must be set to single thread apartment (STA) mode before OLE calls can be made.
            //Ensure that your Main function has STAThreadAttribute marked on it.'
            //if (AutoComplete == true)
            //{
            //    cmb.AutoCompleteMode = AutoCompleteMode.SuggestAppend;
            //    cmb.AutoCompleteSource = AutoCompleteSource.ListItems;
            //}
        }

        public static void FillComboBox(this ComboBox cmb, object dataSource, bool AutoComplete = true)
        {
            cmb.DataSource = null;
            cmb.DisplayMember = nameof(IdAndName.Name);
            cmb.ValueMember = nameof(IdAndName.Id);
            cmb.DataSource = dataSource;
            cmb.SelectedIndex = -1;
            cmb.Format += (s, e) =>
            {
                if (e != null && e.Value != null)
                    if (e.Value.GetType().FullName == typeof(IdAndName).FullName)
                        e.Value = ((IdAndName)e.Value).Name;
            };
            //TODO :
            //Fix System.Threading.ThreadStateException:
            //'Current thread must be set to single thread apartment (STA) mode before OLE calls can be made.
            //Ensure that your Main function has STAThreadAttribute marked on it.'
            //if (AutoComplete == true)
            //{
            //    cmb.AutoCompleteMode = AutoCompleteMode.SuggestAppend;
            //    cmb.AutoCompleteSource = AutoCompleteSource.ListItems;
            //}
        }

        public static void FillComboBox<T>(this MultiColumnComboBox cmb, Expression<Func<T, object>> displayMember, Expression<Func<T, object>> valueMember, object dataSource, bool AutoComplete = true)
        {
            cmb.DataSource = null;
            cmb.DisplayMember = GetCorrectPropertyName(displayMember);
            cmb.ValueMember = GetCorrectPropertyName(valueMember);
            cmb.DataSource = dataSource;
            cmb.SelectedIndex = -1;
            if (AutoComplete == true)
            {
                cmb.AutoCompleteMode = AutoCompleteMode.SuggestAppend;
                cmb.AutoCompleteSource = AutoCompleteSource.ListItems;
            }
            cmb.OpenSearchForm += (s, e) =>
            {
                if (s != null && s is MultiColumnComboBox multiColumnComboBox)
                {
                    FormSearch newForm = new FormSearch(multiColumnComboBox);
                    newForm.ShowDialog();
                }
            };
        }

        public static object? GetSelectedValue<T>(this ComboBox cmb)
        {
            if (cmb.ValueMember != null)
            {
                if (cmb.SelectedValue != null)
                {
                    if (cmb.SelectedValue is T)
                    {
                        Type tableType = typeof(T);
                        PropertyInfo? columnsProperties = tableType.GetProperties().Where(prop => prop.Name == cmb.ValueMember).FirstOrDefault();
                        if (columnsProperties != null)
                        {
                            object? primaryValue = columnsProperties.GetValue(cmb.SelectedValue, null);
                            return primaryValue;
                        }
                    }
                    else
                    {
                        return cmb.SelectedValue;
                    }
                }
            }
            return null;
        }

        public static void SetSelectedValue<T>(this ComboBox cmb, object value)
        {
            if (value != null)
            {
                if (value is int)
                {
                    if ((int)value == 0)
                    {
                        cmb.SelectedIndex = -1;
                        return;
                    }
                }

                if (cmb.ValueMember != null)
                {
                    List<T> Data = new List<T>();
                    switch (cmb.DataSource)
                    {
                        case BindingSource bindingSource:
                            if (bindingSource != null)
                                switch (bindingSource)
                                {
                                    case SyncBindingList<T> syncBindingList:
                                        Data = new List<T>(syncBindingList);
                                        break;
                                }
                            break;
                        case List<T> List:
                            Data = List;
                            break;
                    }

                    if (Data != null)
                    {
                        T? row = Data.Filter(cmb.ValueMember, value.ToString()).FirstOrDefault();
                        if (row != null)
                        {
                            cmb.SelectedValue = row;
                        }
                        if (cmb.SelectedValue == null)
                        {
                            cmb.LookupAndSetValue(value);
                        }
                    }
                }
            }
            else
            {
                cmb.SelectedIndex = -1;
            }
        }

        public static void LookupAndSetValue(this ComboBox combobox, object? value)
        {
            if (combobox.Items.Count > 0)
            {
                for (int i = 0; i < combobox.Items.Count; i++)
                {
                    object? item = combobox.Items[i];
                    object? thisValue = item?.GetType()?.GetProperty(combobox.ValueMember)?.GetValue(item);
                    if (thisValue != null && thisValue.Equals(value))
                    {
                        combobox.SelectedIndex = i;
                        return;
                    }
                }
                // Select first item if requested item was not found
                combobox.SelectedIndex = -1;
            }
        }

        private static string GetCorrectPropertyName<T>(Expression<Func<T, object>> expression)
        {
            if (expression.Body is MemberExpression)
            {
                return ((MemberExpression)expression.Body).Member.Name;
            }
            else
            {
                var op = ((UnaryExpression)expression.Body).Operand;
                return ((MemberExpression)op).Member.Name;
            }
        }
    }
}
