﻿namespace GMCadiomCore.Desktop.CustomControls.dgv
{
    public partial class frmColumnsVisibility : Form
    {
        public delegate void ItemChangedEventHandler(ColumnVisibility columnVisibility);
        public event ItemChangedEventHandler CheckedItemsChanged;

        public List<ColumnVisibility> Items => clbxColumns.Items.OfType<ColumnVisibility>().ToList();

        public frmColumnsVisibility()
        {
            InitializeComponent();

            clbxColumns.ItemCheck += CheckedListBox_ItemCheck;
        }

        private void CheckedListBox_ItemCheck(object? sender, ItemCheckEventArgs e)
        {
            // Trigger CheckedItemsChanged event
            var columnVisibility = clbxColumns.Items[e.Index] as ColumnVisibility;
            if (columnVisibility != null)
            {
                columnVisibility.IsVisible = e.NewValue == CheckState.Checked ? true : false;
                CheckedItemsChanged?.Invoke(columnVisibility);
            }
        }

        public void AddItem(ColumnVisibility columnVisibility)
        {
            var isExist = Items.Find(x => x.ColumnName.Equals(columnVisibility.ColumnName));
            if (isExist == null)
                clbxColumns.Items.Add(columnVisibility, columnVisibility.IsVisible);
            else
            {
                for (int i = 0; i < clbxColumns.Items.Count; i++)
                {
                    ColumnVisibility? item = clbxColumns.Items[i] as ColumnVisibility;
                    if (item != null)
                    {
                        clbxColumns.SetItemChecked(i, item.IsVisible);
                        CheckedItemsChanged?.Invoke(item);
                    }
                }
            }
        }

        public void ClearItems() => clbxColumns.Items.Clear();
    }

    public class ColumnVisibility
    {
        public string ColumnHeaderText { get; set; }
        public string ColumnName { get; set; }
        public bool IsVisible { get; set; }

        public override string ToString() => ColumnHeaderText;
    }
}
