﻿namespace GMCadiomCore.Desktop.Shared.UI
{
    interface IColorsPlate
    {
        Color Level0 { get; }
        Color Level1 { get; }
        Color Level2 { get; }
        Color Level3 { get; }
        Color Level4 { get; }
    }

    public class ColorsPlate1 : IColorsPlate
    {
        public Color Level0 => Color.Transparent;
        public Color Level1 => ColorTranslator.FromHtml("#EEF5FF");
        public Color Level2 => ColorTranslator.FromHtml("#9EB8D9");
        public Color Level3 => ColorTranslator.FromHtml("#7C93C3");
        public Color Level4 => ColorTranslator.FromHtml("#A25772");
    }

    public class ColorsPlate2 : IColorsPlate
    {
        public Color Level0 => Color.Transparent;
        public Color Level1 => ColorTranslator.FromHtml("#22092C");
        public Color Level2 => ColorTranslator.FromHtml("#872341");
        public Color Level3 => ColorTranslator.FromHtml("#BE3144");
        public Color Level4 => ColorTranslator.FromHtml("#F05941");
    }

    public class ColorsPlate3 : IColorsPlate
    {
        public Color Level0 => Color.Transparent;
        public Color Level1 => ColorTranslator.FromHtml("#2B2A4C");
        public Color Level2 => ColorTranslator.FromHtml("#B31312");
        public Color Level3 => ColorTranslator.FromHtml("#EA906C");
        public Color Level4 => ColorTranslator.FromHtml("#EEE2DE");
    }
}
