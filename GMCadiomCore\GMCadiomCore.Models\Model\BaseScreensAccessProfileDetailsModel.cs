﻿namespace GMCadiomCore.Models.Model
{
    [Table("ScreensAccessProfileDetails")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<BaseScreensAccessProfileDetailsModel>))]
    public class BaseScreensAccessProfileDetailsModel : BaseIdentityModel
    {
        private Ulid _ScreensAccessProfileId;
        private Ulid _ScreenId;
        private bool _CanShow;
        private bool _CanOpen;
        private bool _CanAdd;
        private bool _CanEdit;
        private bool _CanDelete;
        private bool _CanPrint;

        [CustomRequired]
        [DisplayName("Screens Access Profile")]
        [Browsable(false)]
        public Ulid ScreensAccessProfileId { get => _ScreensAccessProfileId; set => this.CheckPropertyChanged(ref _ScreensAccessProfileId, ref value); }
        [Browsable(false)]
        public virtual BaseScreensAccessProfileModel ScreensAccessProfile { get; set; } = null!;
        [CustomRequired]
        [DisplayName("Screen Id")]
        [Browsable(false)]
        public Ulid ScreenId { get => _ScreenId; set => CheckPropertyChanged(ref _ScreenId, ref value); }
        [CustomRequired]
        [DisplayName("Can Show")]
        public bool CanShow { get => _CanShow; set => CheckPropertyChanged(ref _CanShow, ref value); }
        [CustomRequired]
        [DisplayName("Can Open")]
        public bool CanOpen { get => _CanOpen; set => CheckPropertyChanged(ref _CanOpen, ref value); }
        [CustomRequired]
        [DisplayName("Can Add")]
        public bool CanAdd { get => _CanAdd; set => CheckPropertyChanged(ref _CanAdd, ref value); }
        [CustomRequired]
        [DisplayName("Can Edit")]
        public bool CanEdit { get => _CanEdit; set => CheckPropertyChanged(ref _CanEdit, ref value); }
        [CustomRequired]
        [DisplayName("Can Delete")]
        public bool CanDelete { get => _CanDelete; set => CheckPropertyChanged(ref _CanDelete, ref value); }
        [CustomRequired]
        [DisplayName("Can Print")]
        public bool CanPrint { get => _CanPrint; set => CheckPropertyChanged(ref _CanPrint, ref value); }
    }
}
