﻿namespace GMCadiomCore.Desktop.CustomControls.cmb
{
    public class cmbBindingComboBox : ComboBox
    {
        public new object? SelectedValue
        {
            get => GetSelectedValue(this);
            set => SetSelectedValue(this, value);
        }

        private object? GetSelectedValue(ComboBox cmb)
        {
            if (cmb.ValueMember != null)
            {
                if (cmb.SelectedValue != null)
                {
                    Type tableType = cmb.SelectedValue.GetType();
                    PropertyInfo? columnsProperties = tableType.GetProperties().Where(prop => prop.Name == cmb.ValueMember).FirstOrDefault();

                    if (columnsProperties != null)
                    {
                        object? primaryValue = columnsProperties.GetValue(cmb.SelectedValue, null);
                        return primaryValue;
                    }
                    else
                    {
                        return cmb.SelectedValue;
                    }
                }
            }
            return null;
        }

        private void SetSelectedValue(ComboBox cmb, object? value)
        {
            if (value != null)
            {
                if (value is int)
                {
                    if ((int)value == 0)
                    {
                        cmb.SelectedIndex = -1;
                        return;
                    }
                }

                if (cmb.ValueMember != null)
                {
                    List<object> Data = new List<object>();
                    switch (cmb.DataSource)
                    {
                        case BindingSource bindingSource:
                            if (bindingSource != null)
                                Data = bindingSource.DataSource.ToList<object>();
                            break;
                        case List<object> List:
                            Data = List;
                            break;
                    }

                    if (Data != null)
                    {
                        object? row = Data.Filter(cmb.ValueMember, value?.ToString()).FirstOrDefault();
                        if (row != null)
                        {
                            cmb.SelectedValue = row;
                        }
                        if (cmb.SelectedValue == null)
                        {
                            cmb.LookupAndSetValue(value);
                        }
                    }
                }
            }
            else
            {
                cmb.SelectedIndex = -1;
            }
        }
    }
}
