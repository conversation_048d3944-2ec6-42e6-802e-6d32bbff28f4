﻿<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs">

  <Package Name="LeadTeams Desktop"
           Language="1033"
           Version="1.7.0"
           Manufacturer="LeadTeams"
           UpgradeCode="5CC746F1-2163-46C3-BF61-60715160DD1E"
           InstallerVersion="200"
           ProductCode="5F9FF2D8-E6C0-46B0-9EC0-8C8A1FAB1630">

    <!-- Upgrade logic to replace existing installations -->
    <MajorUpgrade DowngradeErrorMessage="A newer version of [ProductName] is already installed."
                  Schedule="afterInstallInitialize"
                  AllowSameVersionUpgrades="yes" />

    <!-- Media definition -->
    <Media Id="1" Cabinet="LeadTeamsDesktop.cab" EmbedCab="yes" />

    <!-- Installation directory structure -->
    <StandardDirectory Id="ProgramFiles64Folder">
      <Directory Id="ManufacturerFolder" Name="LeadTeams">
        <Directory Id="INSTALLFOLDER" Name="LeadTeams Desktop" />
      </Directory>
    </StandardDirectory>
    <StandardDirectory Id="ProgramMenuFolder">
      <Directory Id="ApplicationProgramsFolder" Name="LeadTeams Desktop" />
    </StandardDirectory>
    <StandardDirectory Id="DesktopFolder" />

    <!-- Main feature -->
    <Feature Id="ProductFeature" Title="LeadTeams Desktop" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
      <ComponentRef Id="ApplicationShortcut" />
      <ComponentRef Id="DesktopShortcut" />
    </Feature>

    <!-- Application shortcuts -->
    <Component Id="ApplicationShortcut" Directory="ApplicationProgramsFolder">
      <Shortcut Id="ApplicationStartMenuShortcut" Name="LeadTeams Desktop" Description="LeadTeams Desktop Application" Target="[INSTALLFOLDER]LeadTeams.Desktop.exe" WorkingDirectory="INSTALLFOLDER" Icon="LeadTeamsIcon.ico" />
      <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall" />
      <RegistryValue Root="HKCU" Key="Software\LeadTeams\Desktop" Name="installed" Type="integer" Value="1" KeyPath="yes" />
    </Component>

    <Component Id="DesktopShortcut" Directory="DesktopFolder">
      <Shortcut Id="ApplicationDesktopShortcut" Name="LeadTeams Desktop" Description="LeadTeams Desktop Application" Target="[INSTALLFOLDER]LeadTeams.Desktop.exe" WorkingDirectory="INSTALLFOLDER" Icon="LeadTeamsIcon.ico" />
      <RegistryValue Root="HKCU" Key="Software\LeadTeams\Desktop" Name="desktop_shortcut" Type="integer" Value="1" KeyPath="yes" />
    </Component>

    <!-- Icon definition -->
    <Icon Id="LeadTeamsIcon.ico" SourceFile="LeadTeamsIcon.ico" />

    <!-- Add/Remove Programs properties -->
    <Property Id="ARPPRODUCTICON" Value="LeadTeamsIcon.ico" />
    <Property Id="ARPHELPLINK" Value="https://leadteams.com" />
    <Property Id="ARPURLINFOABOUT" Value="https://leadteams.com" />
    <Property Id="ARPNOREPAIR" Value="1" />
    <Property Id="ARPNOMODIFY" Value="1" />

    <!-- Windows Apps integration -->
    <Property Id="ALLUSERS" Value="1" />

  </Package>
</Wix>
