﻿namespace GMCadiomCore.Desktop.Shared.Helper
{
    public static class OpenForm
    {
        public static IEnumerable<ToolStripItem> GetControls(ToolStrip toolStrip)
        {
            foreach (ToolStripItem childToolStripItem in toolStrip.Items)
            {
                yield return childToolStripItem;
            }
        }

        public static IEnumerable<Control> GetControls(Control form)
        {
            foreach (Control childControl in form.Controls)
            {
                foreach (Control grandChild in GetControls(childControl))
                {
                    yield return grandChild;
                }
                yield return childControl;
            }
        }

        public static void InitializeControls(Control frm)
        {
            foreach (Control ctrl in GetControls(frm))
            {
                if (ctrl.Tag is string tagString && tagString != "Need")
                {
                    if (ctrl is ToolStrip)
                    {
                    }
                    else if (ctrl is Label)
                    {
                    }
                    else if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).ResetText();
                        ((TextBox)ctrl).Text = "";
                    }
                    else if (ctrl is ComboBox)
                    {
                        ((ComboBox)ctrl).SelectedIndex = -1;
                        ((ComboBox)ctrl).Text = "";
                    }
                    else if (ctrl is CheckBox)
                    {
                        ((CheckBox)ctrl).Checked = false;
                    }
                    else if (ctrl is ListBox)
                    {
                        ((ListBox)ctrl).ClearSelected();
                    }
                    else if (ctrl is DataGridView)
                    {
                        ((DataGridView)ctrl).DataSource = null;
                        if (((DataGridView)ctrl).Rows.Count > 0)
                        {
                            ((DataGridView)ctrl).CancelEdit();
                            ((DataGridView)ctrl).Rows.Clear();
                        }
                    }
                    else if (ctrl is DateTimePicker)
                    {
                        ((DateTimePicker)ctrl).Value = DateTime.Today.Date;
                    }
                }
            }
        }
    }
}
