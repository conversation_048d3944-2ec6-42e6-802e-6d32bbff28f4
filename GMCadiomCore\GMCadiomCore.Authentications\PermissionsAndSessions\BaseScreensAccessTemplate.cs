﻿namespace GMCadiomCore.Authentications.PermissionsAndSessions
{
    public enum Actions
    {
        Show = 1,
        Open,
        Add,
        Edit,
        Delete,
        Print,
    }

    [Table("ScreensAccessTemplate")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<BaseScreensAccessTemplate>))]
    public class BaseScreensAccessTemplate : BaseScreensAccessProfileDetailsModel
    {
        //Used For TypeDescriptionProvider
        public BaseScreensAccessTemplate() { }
        public BaseScreensAccessTemplate(Ulid screenId, BaseScreensAccessTemplate? parent = null, [CallerMemberName] string? name = "")
        {
            if (name != null)
                ScreenName = name;
            ScreenId = screenId;
            if (parent != null)
                ParentScreenId = parent.ScreenId;
            else ParentScreenId = Ulid.Empty;
            CanShow = true;
            Actions = new List<Actions>()
            {
                PermissionsAndSessions.Actions.Add,
                PermissionsAndSessions.Actions.Edit,
                PermissionsAndSessions.Actions.Delete,
                PermissionsAndSessions.Actions.Print,
                PermissionsAndSessions.Actions.Show,
                PermissionsAndSessions.Actions.Open,
            };
        }
        [Browsable(false)]
        public bool HasChild { get; set; }
        [Browsable(false)]
        public Ulid ParentScreenId { get; set; }
        [Browsable(false)]
        public string ScreenName { get; set; }
        [DisplayName("Screen Text")]
        public string ScreenText { get; set; }
        public List<Actions> Actions { get; set; }
    }
}
