﻿namespace GMCadiomCore.Desktop.CustomControls.dtp
{
    public class dtpBindingDateTimePicker : DateTimePicker
    {
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        private new DateTime Value { get => base.Value; set => base.Value = value; }
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public DateTime ValueDateTime { get => Value; set => Value = value; }
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public DateTime? ValueDateTimeNullable { get => GetValueDateTimeNullable(); set => SetValueDateTimeNullable(value); }
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public TimeSpan ValueTimeSpan { get => ValueDateTime.TimeOfDay; set => ValueDateTime = DateTimeFromTimeSpan(DateTime.Now, value); }
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public TimeSpan? ValueTimeSpanNullable { get => GetValueTimeSpanNullable(); set => SetValueTimeSpanNullable(value); }

        private DateTime? GetValueDateTimeNullable()
        {
            if (!Checked)
                return null;

            return ValueDateTime;
        }

        private void SetValueDateTimeNullable(DateTime? value)
        {
            if (value is null)
            {
                Checked = false;
                return;
            }

            Checked = true;
            ValueDateTime = value.Value;
        }

        private TimeSpan? GetValueTimeSpanNullable()
        {
            if (!Checked)
                return null;

            return ValueTimeSpan;
        }

        private void SetValueTimeSpanNullable(TimeSpan? value)
        {
            if (value is null)
            {
                Checked = false;
                return;
            }

            Checked = true;
            ValueTimeSpan = value.Value;
        }

        protected override void OnValueChanged(EventArgs eventargs)
        {
            base.OnValueChanged(eventargs);
        }

        protected override void OnTextChanged(EventArgs e)
        {
            base.OnTextChanged(e);
        }

        private DateTime DateTimeFromTimeSpan(DateTime dateTime, TimeSpan timeSpan)
        {
            return new DateTime(dateTime.Year, dateTime.Month, dateTime.Day, timeSpan.Hours, timeSpan.Minutes, timeSpan.Seconds);
        }
    }
}
