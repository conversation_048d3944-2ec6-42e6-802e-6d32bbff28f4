﻿namespace GMCadiomCore.Desktop.Loading
{
    public class LoadingHelper
    {
        /// <summary>
        /// Start loading
        /// </summary>
        /// <param name="message">information</param>
        /// <param name="ownerForm">Parent Form</param>
        /// <param name="work">Work to be performed</param>
        /// <param name="workArg">Working Parameters</param>
        public static void ShowLoading(string message, Form ownerForm, ParameterizedThreadStart work, object? workArg = null)
        {
            var loadingForm = new LoadingView(message);
            dynamic expandoObject = new ExpandoObject();
            expandoObject.Form = loadingForm;
            expandoObject.WorkArg = workArg;
            loadingForm.SetWorkAction(work, expandoObject);
            loadingForm.ShowDialog(ownerForm);
            if (loadingForm.WorkException != null)
            {
                throw loadingForm.WorkException;
            }
        }

        /// <summary>
        /// Start loading
        /// </summary>
        /// <param name="message">information</param>
        /// <param name="ownerForm">Parent Form</param>
        /// <param name="work">Work to be performed</param>
        /// <param name="workArg">Working Parameters</param>
        public static void ShowLoading(string message, ParameterizedThreadStart work, object? workArg = null)
        {
            var loadingForm = new LoadingView(message);
            dynamic expandoObject = new ExpandoObject();
            expandoObject.Form = loadingForm;
            expandoObject.WorkArg = workArg;
            loadingForm.SetWorkAction(work, expandoObject);
            loadingForm.ShowDialog();
            if (loadingForm.WorkException != null)
            {
                throw loadingForm.WorkException;
            }
        }

        /// <summary>
        /// Start loading
        /// </summary>
        /// <param name="message">information</param>
        /// <param name="ownerForm">Parent Form</param>
        /// <param name="work">Work to be performed</param>
        /// <param name="workArg">Working Parameters</param>
        public static void ShowLoading(string message, Form ownerForm)
        {
            var loadingForm = new LoadingView(message);
            loadingForm.ShowDialog(ownerForm);
            if (loadingForm.WorkException != null)
            {
                throw loadingForm.WorkException;
            }
        }
    }
}
