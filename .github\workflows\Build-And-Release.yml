name: Build And Release

permissions:
  contents: write

on:
  workflow_dispatch:
  push:
    branches:
    - master

env:
  projectName: 'LeadTeams'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'
  runtime: 'win-x64'
  framework: 'net8.0-windows'
  adminBuildPath: ''
  clientBuildPath: ''

jobs:
  Build:
    runs-on: windows-latest
    steps:
    - name: Set solution env var
      run: echo "solution=${{ env.projectName }}.sln" >> $env:GITHUB_ENV

    - name: Set buildPath env vars
      run: |
        echo "adminBuildPath=LeadTeamsDesktopSetup/${{ env.buildConfiguration }}" >> $env:GITHUB_ENV
        echo "clientBuildPath=LeadTeamsClientSetup/${{ env.buildConfiguration }}" >> $env:GITHUB_ENV

    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
        submodules: false
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Checkout GMCadiomCore Submodule
      uses: actions/checkout@v4
      with:
        repository: GMCadiom/GMCadiomCore
        token: ${{ secrets.GMCadiomCore_TOKEN }}
        path: GMCadiomCore

    - name: Checkout SignalRChat Submodule
      uses: actions/checkout@v4
      with:
        repository: GMCadiom/SignalRChat
        token: ${{ secrets.SignalRChat_TOKEN }}
        path: SignalRChat

    - name: Set up Visual Studio
      uses: microsoft/setup-msbuild@v1.0.2

    - name: Setup .NET Core SDK .NET 8
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: 8.0.x

    - name: Update Submodules
      run: git submodule update --init --recursive

    - name: Clean NuGet Cache
      run: dotnet nuget locals all --clear

    - name: Restore Tools
      run: dotnet tool restore

    - name: Clean Build Artifacts
      run: git clean -xfd

    - name: Restore dependencies 1
      run: dotnet restore

    - name: Restore dependencies 2
      run: msbuild -property:Configuration=${{ env.buildConfiguration }} -t:restore -p:RestorePackagesConfig=true

    - name: Build Admin Setup Project For Release
      run: |
        $VSPATH=& "${Env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere" -property installationPath
        $DEVENV_COM="$VSPATH\Common7\IDE\devenv"
        & $DEVENV_COM ${{ env.projectName }}.sln /Build "${{ env.buildConfiguration }}|${{ env.buildPlatform }}" /Project .\LeadTeamsDesktopSetup\LeadTeamsDesktopSetup.vdproj

    - name: Build Client Setup Project For Release
      run: |
        $VSPATH=& "${Env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere" -property installationPath
        $DEVENV_COM="$VSPATH\Common7\IDE\devenv"
        & $DEVENV_COM ${{ env.projectName }}.sln /Build "${{ env.buildConfiguration }}|${{ env.buildPlatform }}" /Project .\LeadTeamsClientSetup\LeadTeamsClientSetup.vdproj

    - name: Zip Admin Output
      uses: vimtor/action-zip@v1
      with:
        files: "./${{ env.adminBuildPath }}/"
        recursive: false
        dest: ./${{ env.adminBuildPath }}/AdminSetup.zip

    - name: Zip Client Output
      uses: vimtor/action-zip@v1
      with:
        files: "./${{ env.clientBuildPath }}/"
        recursive: false
        dest: ./${{ env.clientBuildPath }}/ClientSetup.zip

    - name: Setup git
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GMCadiom"

    - name: add changes on .husky/_/husky.sh
      run: git add .husky/_/husky.sh

    - name: install Versionize
      run: dotnet tool install --global Versionize

    - name: Versionize Release
      id: versionize
      run: versionize --ignore-insignificant-commits
      continue-on-error: true

    - name: No release required 
      if: steps.versionize.outcome != 'success'
      run: echo "Skipping Release. No release required."

    - name: Push changes to GitHub
      if: steps.versionize.outcome == 'success'
      uses: ad-m/github-push-action@master
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        branch: ${{ github.ref }}
        tags: true

    - name: "Create release"
      if: steps.versionize.outcome == 'success'
      uses: "actions/github-script@v5" 
      with:
        github-token: "${{ secrets.GITHUB_TOKEN }}"
        script: |
          try {
            const fs = require('fs');
            const tags_url = context.payload.repository.tags_url + "?per_page=1"
            const result = await github.request(tags_url)
            const current_tag = result.data[0].name
            const release = await github.rest.repos.createRelease({
              draft: false,
              generate_release_notes: true,
              name: current_tag,
              owner: context.repo.owner,
              prerelease: false,
              repo: context.repo.repo,
              tag_name: current_tag,
            });
            
            // Upload Admin ZIP
            let sourceAdmin = fs.readFileSync('./${{ env.adminBuildPath }}/AdminSetup.zip');
            for (let retry = 0; retry < 5; retry++) {
              try {
                const uploadAdmin = await github.rest.repos.uploadReleaseAsset({
                  owner: context.repo.owner, 
                  repo: context.repo.repo, 
                  release_id: release.data.id,
                  name: 'Admin-' + current_tag + '.zip', 
                  headers: {'Content-Type': 'application/zip'}, 
                  data: sourceAdmin
                });
                break;
              } catch (error) {
                console.log(`Failed to upload Admin ZIP (status ${error.status})`);
                if (error.status != 500) {
                  throw error;
                }
              }
            }

            // Upload Client ZIP
            let sourceClient = fs.readFileSync('./${{ env.clientBuildPath }}/ClientSetup.zip');
            for (let retry = 0; retry < 5; retry++) {
              try {
                const uploadClient = await github.rest.repos.uploadReleaseAsset({
                  owner: context.repo.owner, 
                  repo: context.repo.repo, 
                  release_id: release.data.id,
                  name: 'Client-' + current_tag + '.zip', 
                  headers: {'Content-Type': 'application/zip'}, 
                  data: sourceClient
                });
                break;
              } catch (error) {
                console.log(`Failed to upload Client ZIP (status ${error.status})`);
                if (error.status != 500) {
                  throw error;
                }
              }
            }

            const update = await github.rest.repos.updateRelease({
              owner: context.repo.owner, 
              repo: context.repo.repo, 
              release_id: release.data.id,
              draft: false
            });
          } catch (error) {
            core.setFailed(error.message);
          }
