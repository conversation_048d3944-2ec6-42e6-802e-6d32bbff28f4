﻿namespace GMCadiomCore.Presenters.View.Base
{
    public interface IBaseView<T> : IBaseView
    {
        //Properties - Fields
        T CurrentModel { get; set; }
    }

    public interface IBaseView
    {
        //Properties - Fields
        Ulid Id { get; set; }
        bool IsEdit { get; set; }
        bool IsSuccessful { get; set; }
        string Message { set; }
        BindingSource TempList { get; set; }

        //Events
        event EventHandler AddNewEvent;
        event EventHandler SaveEvent;
        event EventHandler DeleteEvent;
        event EventHandler SaveTempEvent;
        event EventHandler GetTempEvent;

        //Methods
        void Show();
        DialogResult ShowDialog();
    }
}
