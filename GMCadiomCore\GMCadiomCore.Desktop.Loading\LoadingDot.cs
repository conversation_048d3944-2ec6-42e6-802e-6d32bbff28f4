﻿namespace GMCadiomCore.Desktop.Loading
{
    /// <summary>  
    /// Represents a "point"
    /// </summary>  
    internal sealed class LoadingDot
    {
        #region Fields/Properties 

        [Description("Center")] private readonly PointF _circleCenter;
        [Description("radius")] private readonly float _circleRadius;

        /// <summary>  
        /// The current frame drawing coordinates are recalculated each time DoAction() is executed.  
        /// </summary>  
        public PointF Location;

        [Description("The angle of the point relative to the center of the circle, used to calculate the drawing coordinates of the point")] private int _angle;
        [Description("transparency")] private int _opacity;
        [Description("Animation progress")] private int _progress;
        [Description("speed")] private int _speed;

        [Description("transparency")]
        public int Opacity => _opacity < MinOpacity ? MinOpacity : _opacity > MaxOpacity ? MaxOpacity : _opacity;

        #endregion

        #region constant  

        [Description("Minimum Speed")] private const int MinSpeed = 2;
        [Description("Maximum speed")] private const int MaxSpeed = 11;

        [Description("Relative angle of the appearance area")] private const int AppearAngle = 90;
        [Description("Relative angle of the deceleration zone")] private const int SlowAngle = 225;
        [Description("Relative angle of acceleration zone")] private const int QuickAngle = 315;

        [Description("Minimum angle")] private const int MinAngle = 0;
        [Description("Maximum Angle")] private const int MaxAngle = 360;

        [Description("Fade out speed")] private const int AlphaSub = 25;

        [Description("Minimum transparency")] private const int MinOpacity = 0;
        [Description("Maximum transparency")] private const int MaxOpacity = 255;

        #endregion constant  

        #region Constructor  

        public LoadingDot(PointF circleCenter, float circleRadius)
        {
            Reset();
            _circleCenter = circleCenter;
            _circleRadius = circleRadius;
        }

        #endregion Constructor  

        #region method  

        /// <summary>  
        /// Recalculate the current frame drawing coordinates
        /// </summary>  
        private void ReCalcLocation()
        {
            Location = GetDotLocationByAngle(_circleCenter, _circleRadius, _angle);
        }

        /// <summary>  
        /// Point Action
        /// </summary>  
        public void LoadingDotAction()
        {
            switch (_progress)
            {
                case 0:
                    {
                        _opacity = MaxOpacity;
                        AddSpeed();
                        if (_angle + _speed >= SlowAngle && _angle + _speed < QuickAngle)
                        {
                            _progress = 1;
                            _angle = SlowAngle - _speed;
                        }
                    }
                    break;
                case 1:
                    {
                        SubSpeed();
                        if (_angle + _speed >= QuickAngle || _angle + _speed < SlowAngle)
                        {
                            _progress = 2;
                            _angle = QuickAngle - _speed;
                        }
                    }
                    break;
                case 2:
                    {
                        AddSpeed();
                        if (_angle + _speed >= SlowAngle && _angle + _speed < QuickAngle)
                        {
                            _progress = 3;
                            _angle = SlowAngle - _speed;
                        }
                    }
                    break;
                case 3:
                    {
                        SubSpeed();
                        if (_angle + _speed >= QuickAngle && _angle + _speed < MaxAngle)
                        {
                            _progress = 4;
                            _angle = QuickAngle - _speed;
                        }
                    }
                    break;
                case 4:
                    {
                        SubSpeed();
                        if (_angle + _speed >= MinAngle && _angle + _speed < AppearAngle)
                        {
                            _progress = 5;
                            _angle = MinAngle;
                        }
                    }
                    break;
                case 5:
                    {
                        AddSpeed();
                        FadeOut();
                    }
                    break;
            }

            //move  
            _angle = _angle >= MaxAngle - _speed ? MinAngle : _angle + _speed;
            //Recalculate coordinates  
            ReCalcLocation();
        }

        /// <summary>
        /// fade out
        /// </summary>
        private void FadeOut()
        {
            if ((_opacity -= AlphaSub) <= 0)
                _angle = AppearAngle;
        }


        /// <summary>
        /// Reset state
        /// </summary>
        public void Reset()
        {
            _angle = AppearAngle;
            _speed = MinSpeed;
            _progress = 0;
            _opacity = 1;
        }

        /// <summary>
        /// accelerate
        /// </summary>
        private void AddSpeed()
        {
            if (++_speed >= MaxSpeed) _speed = MaxSpeed;
        }

        /// <summary>
        /// slow down
        /// </summary>
        private void SubSpeed()
        {
            if (--_speed <= MinSpeed) _speed = MinSpeed;
        }

        #endregion method  

        /// <summary>  
        /// Find the coordinates on the circle based on the radius and angle
        /// </summary>  
        /// <param name="center">Center</param>  
        /// <param name="radius">radius</param>  
        /// <param name="angle">angle</param>  
        /// <returns>coordinate</returns>  
        public static PointF GetDotLocationByAngle(PointF center, float radius, int angle)
        {
            var x = (float)(center.X + radius * Math.Cos(angle * Math.PI / 180));
            var y = (float)(center.Y + radius * Math.Sin(angle * Math.PI / 180));

            return new PointF(x, y);
        }
    }
}
